2025-08-04 17:10:57,175 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:10:57,176 - INFO - ================================================================================
2025-08-04 17:10:57,176 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:10:57,176 - INFO - ================================================================================
2025-08-04 17:10:57,176 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:10:57,256 - INFO - 找到有效目标 1: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-04 17:10:57,258 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:10:57,258 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:10:57,258 - INFO - ============================================================
2025-08-04 17:10:57,259 - INFO - 📍 处理第 1/1 个公众号: 南京税务
2025-08-04 17:10:57,259 - INFO - ============================================================
2025-08-04 17:10:57,259 - INFO - [步骤 1/5] 为 '南京税务' 创建独立的 Cookie 抓取器...
2025-08-04 17:10:57,259 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:10:57,259 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:10:57,259 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:10:57,259 - INFO - === 开始重置网络状态 ===
2025-08-04 17:10:57,259 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:10:57,349 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:10:57,349 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:10:57,350 - INFO - 系统代理已成功关闭
2025-08-04 17:10:57,350 - INFO - ✅ 代理关闭操作
2025-08-04 17:10:57,350 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:10:58,292 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:10:58,292 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:10:58,292 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:10:58,293 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:10:58,293 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:10:58,786 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:10:58,787 - INFO - 🔄 Cookie抓取器进程已启动，PID: 35612
2025-08-04 17:10:58,787 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:11:01,787 - INFO - 等待代理服务启动...
2025-08-04 17:11:02,243 - INFO - 代理服务已启动并正常工作
2025-08-04 17:11:02,243 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 35612)
2025-08-04 17:11:02,243 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:11:02,243 - INFO - [步骤 2/5] 为 '南京税务' 启动 UI 自动化...
2025-08-04 17:11:02,244 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:11:02,244 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:11:02,244 - INFO - 正在查找微信主窗口...
2025-08-04 17:11:03,231 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:11:03,231 - INFO - 正在激活微信窗口...
2025-08-04 17:11:05,737 - INFO - 微信窗口已激活。
2025-08-04 17:11:05,737 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:11:12,455 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:11:12,456 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:11:15,787 - INFO - 正在查找聊天输入框...
2025-08-04 17:11:17,788 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:11:17,792 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:11:17,793 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:11:19,383 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&mid=2650808386&idx=1&sn=968d6981cb041399053104db0fd8ca79&chksm=bd05b6488a723f5e7243ed8f14aa4f93acc11b592c6e837bfac98061b8740f039d4f3305182f#rd
2025-08-04 17:11:21,673 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:11:21,928 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:11:22,737 - INFO - 链接已发送。
2025-08-04 17:11:25,738 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:11:27,826 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:11:28,553 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:11:31,554 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:11:31,555 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:11:31,556 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:11:31,562 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:11:34,268 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:11:35,769 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:11:43,133 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:11:43,133 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:11:43,133 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:11:43,133 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:11:43,133 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:43,134 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:11:43,134 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:11:43,134 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:43,134 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:11:43,134 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:11:43,134 - INFO - [步骤 3/5] 等待 '南京税务' 的 Cookie 数据...
2025-08-04 17:11:43,134 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:11:44,135 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:11:44,137 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:44,137 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:11:44,138 - INFO - [步骤 4/5] 停止 '南京税务' 的 Cookie 抓取器...
2025-08-04 17:11:44,138 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:11:44,139 - INFO - 正在停止Cookie抓取器 (PID: 35612)...
2025-08-04 17:11:44,141 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:11:44,141 - INFO - 正在验证并清理代理设置...
2025-08-04 17:11:44,141 - INFO - === 开始重置网络状态 ===
2025-08-04 17:11:44,142 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:11:44,269 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:11:44,269 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:11:44,269 - INFO - 系统代理已成功关闭
2025-08-04 17:11:44,269 - INFO - ✅ 代理关闭操作
2025-08-04 17:11:44,269 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:11:46,057 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:11:46,058 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:11:46,058 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:11:47,770 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:11:47,771 - INFO - ✅ 网络连接验证正常
2025-08-04 17:11:50,772 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:11:50,773 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:12:05,774 - INFO - [步骤 5/5] 开始爬取 '南京税务' 的文章...
2025-08-04 17:12:05,775 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:12:05,889 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
