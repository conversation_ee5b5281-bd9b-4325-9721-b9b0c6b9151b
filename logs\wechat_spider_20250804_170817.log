2025-08-04 17:08:17,164 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:08:17,164 - INFO - ================================================================================
2025-08-04 17:08:17,164 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:08:17,164 - INFO - ================================================================================
2025-08-04 17:08:17,164 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:08:17,240 - INFO - 找到有效目标 1: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-04 17:08:17,242 - INFO - 找到有效目标 2: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-04 17:08:17,243 - INFO - 找到有效目标 3: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-04 17:08:17,243 - INFO - 找到有效目标 4: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-04 17:08:17,243 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:08:17,243 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:08:17,243 - INFO - ============================================================
2025-08-04 17:08:17,243 - INFO - 📍 处理第 1/4 个公众号: 南京工会会员
2025-08-04 17:08:17,243 - INFO - ============================================================
2025-08-04 17:08:17,243 - INFO - [步骤 1/5] 为 '南京工会会员' 创建独立的 Cookie 抓取器...
2025-08-04 17:08:17,244 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:08:17,244 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:08:17,244 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:08:17,244 - INFO - === 开始重置网络状态 ===
2025-08-04 17:08:17,244 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:08:17,359 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:08:17,360 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:08:17,360 - INFO - 系统代理已成功关闭
2025-08-04 17:08:17,360 - INFO - ✅ 代理关闭操作
2025-08-04 17:08:17,360 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:08:20,220 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:08:20,221 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:08:20,222 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:08:20,222 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:08:20,223 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:08:20,756 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:08:20,757 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33800
2025-08-04 17:08:20,757 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:08:23,757 - INFO - 等待代理服务启动...
2025-08-04 17:08:24,222 - INFO - 代理服务已启动并正常工作
2025-08-04 17:08:24,222 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33800)
2025-08-04 17:08:24,223 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:08:24,223 - INFO - [步骤 2/5] 为 '南京工会会员' 启动 UI 自动化...
2025-08-04 17:08:24,223 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:08:24,224 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:08:24,224 - INFO - 正在查找微信主窗口...
2025-08-04 17:08:25,326 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:08:25,326 - INFO - 正在激活微信窗口...
2025-08-04 17:08:27,840 - INFO - 微信窗口已激活。
2025-08-04 17:08:27,841 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:08:34,539 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:08:34,539 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:08:37,864 - INFO - 正在查找聊天输入框...
2025-08-04 17:08:39,865 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:08:39,876 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:08:39,876 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:08:41,461 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&mid=2247667614&idx=1&sn=4488f6e1afcbdb1b49eec9f270562f5e&chksm=ec4b9db7db3c14a1943b9f0e52b6650c218685563b802583089ce84c48d54580c69e663b934c#rd
2025-08-04 17:08:43,751 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:08:43,994 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:08:44,805 - INFO - 链接已发送。
2025-08-04 17:08:47,806 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:08:49,898 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:08:50,620 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:08:53,622 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:08:53,622 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:08:53,622 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:08:53,625 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:08:56,329 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:08:57,830 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:09:05,221 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:09:05,221 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:09:05,221 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:09:05,221 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:09:05,222 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:05,222 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:09:05,222 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:09:05,222 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:05,222 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:09:05,222 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:09:05,222 - INFO - [步骤 3/5] 等待 '南京工会会员' 的 Cookie 数据...
2025-08-04 17:09:05,222 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:09:06,223 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:09:06,223 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:06,224 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:09:06,224 - INFO - [步骤 4/5] 停止 '南京工会会员' 的 Cookie 抓取器...
2025-08-04 17:09:06,224 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:09:06,225 - INFO - 正在停止Cookie抓取器 (PID: 33800)...
2025-08-04 17:09:06,226 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:09:06,227 - INFO - 正在验证并清理代理设置...
2025-08-04 17:09:06,227 - INFO - === 开始重置网络状态 ===
2025-08-04 17:09:06,227 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:09:06,328 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:09:06,328 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:09:06,328 - INFO - 系统代理已成功关闭
2025-08-04 17:09:06,329 - INFO - ✅ 代理关闭操作
2025-08-04 17:09:06,329 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:09:07,244 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:09:07,245 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:09:07,245 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:09:10,094 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:09:10,094 - INFO - ✅ 网络连接验证正常
2025-08-04 17:09:13,095 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:09:13,095 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:09:28,096 - INFO - [步骤 5/5] 开始爬取 '南京工会会员' 的文章...
2025-08-04 17:09:28,096 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:09:28,573 - INFO - ✅ Cookie验证成功，开始正式爬取...
