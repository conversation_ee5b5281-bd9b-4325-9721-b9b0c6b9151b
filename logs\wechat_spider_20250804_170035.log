2025-08-04 17:00:35,770 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:00:35,771 - INFO - ================================================================================
2025-08-04 17:00:35,771 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:00:35,771 - INFO - ================================================================================
2025-08-04 17:00:35,771 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:00:35,844 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 17:00:35,846 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 17:00:35,846 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 17:00:35,847 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 17:00:35,847 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:00:35,847 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:00:35,847 - INFO - ============================================================
2025-08-04 17:00:35,848 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 17:00:35,848 - INFO - ============================================================
2025-08-04 17:00:35,848 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 17:00:35,848 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:00:35,848 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:00:35,848 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:00:35,849 - INFO - === 开始重置网络状态 ===
2025-08-04 17:00:35,849 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:00:35,943 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:00:35,943 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:00:35,943 - INFO - 系统代理已成功关闭
2025-08-04 17:00:35,943 - INFO - ✅ 代理关闭操作
2025-08-04 17:00:35,943 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:00:37,615 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:00:37,616 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:00:37,617 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:00:37,618 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:00:37,619 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:00:38,141 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:00:38,142 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21820
2025-08-04 17:00:38,142 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:00:41,142 - INFO - 等待代理服务启动...
2025-08-04 17:00:46,550 - INFO - 代理服务已启动并正常工作
2025-08-04 17:00:46,551 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21820)
2025-08-04 17:00:46,552 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:00:46,552 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 17:00:46,553 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:00:46,553 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:00:46,553 - INFO - 正在查找微信主窗口...
2025-08-04 17:00:48,436 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:00:48,437 - INFO - 正在激活微信窗口...
2025-08-04 17:00:50,946 - INFO - 微信窗口已激活。
2025-08-04 17:00:50,947 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:00:57,773 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:00:57,774 - INFO - 清空搜索框并将焦点转移到聊天区域...
