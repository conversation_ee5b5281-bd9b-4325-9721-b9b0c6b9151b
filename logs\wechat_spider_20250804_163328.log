2025-08-04 16:33:28,248 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:33:28,248 - INFO - ================================================================================
2025-08-04 16:33:28,248 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:33:28,248 - INFO - ================================================================================
2025-08-04 16:33:28,249 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 16:33:28,331 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 16:33:28,331 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:33:28,331 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:33:28,331 - INFO - ============================================================
2025-08-04 16:33:28,331 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 16:33:28,331 - INFO - ============================================================
2025-08-04 16:33:28,331 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 16:33:28,331 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:33:28,332 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:33:28,332 - INFO - === 开始重置网络状态 ===
2025-08-04 16:33:28,332 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:33:28,421 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:33:28,422 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:33:28,422 - INFO - 系统代理已成功关闭
2025-08-04 16:33:28,422 - INFO - ✅ 代理关闭操作
2025-08-04 16:33:28,422 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:33:30,999 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:33:30,999 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:33:30,999 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:33:31,000 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:33:31,000 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:33:31,501 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:33:31,502 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23040
2025-08-04 16:33:31,502 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:33:34,503 - INFO - 等待代理服务启动...
2025-08-04 16:33:35,972 - INFO - 代理服务已启动并正常工作
2025-08-04 16:33:35,973 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23040)
2025-08-04 16:33:35,973 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:33:35,974 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 16:33:35,974 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:33:35,974 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:33:35,975 - INFO - 正在查找微信主窗口...
2025-08-04 16:33:36,058 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:33:36,058 - INFO - 正在激活微信窗口...
2025-08-04 16:33:38,574 - INFO - 微信窗口已激活。
2025-08-04 16:33:38,575 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:33:45,320 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:33:45,321 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:33:48,649 - INFO - 正在查找聊天输入框...
2025-08-04 16:33:50,650 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:33:50,665 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:33:50,666 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:33:52,265 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&mid=2247548170&idx=1&sn=56ac1f201d459218024a9d219d0a8099&chksm=96dd984ca1aa115a7469db8d4f401c5f3119965621cfd35de2e1adadb482f938f458f9b7896d#rd
2025-08-04 16:33:54,553 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:33:54,789 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:33:55,604 - INFO - 链接已发送。
2025-08-04 16:33:58,605 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:34:00,696 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:34:01,420 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:34:04,421 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:34:04,421 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:34:04,422 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:34:04,434 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:34:07,140 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:34:08,642 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:34:15,770 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:34:15,770 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:34:15,770 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:34:15,770 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:34:15,771 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:15,771 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:34:15,771 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:34:15,771 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:15,771 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:34:15,771 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:34:15,771 - INFO - [步骤 3/5] 等待 '法润栖霞' 的 Cookie 数据...
2025-08-04 16:34:15,771 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:34:16,772 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:34:16,773 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:16,773 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:34:16,773 - INFO - [步骤 4/5] 停止 '法润栖霞' 的 Cookie 抓取器...
2025-08-04 16:34:16,774 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:34:16,774 - INFO - 正在停止Cookie抓取器 (PID: 23040)...
2025-08-04 16:34:16,776 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:34:16,777 - INFO - 正在验证并清理代理设置...
2025-08-04 16:34:16,777 - INFO - === 开始重置网络状态 ===
2025-08-04 16:34:16,777 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:34:16,871 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:34:16,871 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:34:16,871 - INFO - 系统代理已成功关闭
2025-08-04 16:34:16,871 - INFO - ✅ 代理关闭操作
2025-08-04 16:34:16,871 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:34:18,800 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:18,801 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:34:18,801 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:34:20,575 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:20,575 - INFO - ✅ 网络连接验证正常
2025-08-04 16:34:23,576 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:34:23,577 - INFO - [步骤 5/5] 开始爬取 '法润栖霞' 的文章...
2025-08-04 16:34:23,696 - WARNING - ⚠️ 公众号 '法润栖霞' 未获取到任何文章数据
2025-08-04 16:34:23,697 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:34:38,697 - INFO - ============================================================
2025-08-04 16:34:38,698 - INFO - 📍 处理第 2/4 个公众号: 雨花司法
2025-08-04 16:34:38,699 - INFO - ============================================================
2025-08-04 16:34:38,700 - INFO - [步骤 1/5] 为 '雨花司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:34:38,701 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:34:38,701 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:34:38,702 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:34:38,702 - INFO - === 开始重置网络状态 ===
2025-08-04 16:34:38,702 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:34:38,786 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:34:38,786 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:34:38,787 - INFO - 系统代理已成功关闭
2025-08-04 16:34:38,787 - INFO - ✅ 代理关闭操作
2025-08-04 16:34:38,787 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:34:40,724 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:40,724 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:34:40,725 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:34:40,725 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:34:40,726 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:34:41,225 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:34:41,225 - INFO - 🔄 Cookie抓取器进程已启动，PID: 39848
2025-08-04 16:34:41,226 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:34:44,226 - INFO - 等待代理服务启动...
2025-08-04 16:34:44,715 - INFO - 代理服务已启动并正常工作
2025-08-04 16:34:44,716 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 39848)
2025-08-04 16:34:44,716 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:34:44,716 - INFO - [步骤 2/5] 为 '雨花司法' 启动 UI 自动化...
2025-08-04 16:34:44,717 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:34:44,717 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:34:44,718 - INFO - 正在查找微信主窗口...
2025-08-04 16:34:44,936 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:34:44,936 - INFO - 正在激活微信窗口...
2025-08-04 16:34:47,445 - INFO - 微信窗口已激活。
2025-08-04 16:34:47,446 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:34:54,123 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:34:54,123 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:34:57,450 - INFO - 正在查找聊天输入框...
2025-08-04 16:34:59,452 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:34:59,462 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:34:59,462 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:35:01,035 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&mid=2247544684&idx=2&sn=ee23948433c816bc0616572d322a1737&chksm=eba92461dcdead77bee6ed2a6f90ee7824edab22aab2d8c2df6e984090d4cd4eb5c2173b662d#rd
2025-08-04 16:35:03,324 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:35:03,541 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:35:04,355 - INFO - 链接已发送。
2025-08-04 16:35:07,356 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:35:09,441 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:35:10,171 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:35:13,172 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:35:13,173 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:35:13,173 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:35:13,180 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:35:15,885 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:35:17,386 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:35:24,707 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:35:24,707 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:35:24,707 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:35:24,707 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:35:24,707 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:24,707 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:35:24,707 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:35:24,708 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:24,708 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:35:24,708 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:35:24,708 - INFO - [步骤 3/5] 等待 '雨花司法' 的 Cookie 数据...
2025-08-04 16:35:24,708 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:35:25,708 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:35:25,710 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:25,710 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:35:25,711 - INFO - [步骤 4/5] 停止 '雨花司法' 的 Cookie 抓取器...
2025-08-04 16:35:25,711 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:35:25,712 - INFO - 正在停止Cookie抓取器 (PID: 39848)...
2025-08-04 16:35:25,714 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:35:25,715 - INFO - 正在验证并清理代理设置...
2025-08-04 16:35:25,716 - INFO - === 开始重置网络状态 ===
2025-08-04 16:35:25,716 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:35:25,807 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:35:25,808 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:35:25,808 - INFO - 系统代理已成功关闭
2025-08-04 16:35:25,808 - INFO - ✅ 代理关闭操作
2025-08-04 16:35:25,808 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:35:26,730 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:26,731 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:35:26,731 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:35:28,714 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:28,715 - INFO - ✅ 网络连接验证正常
2025-08-04 16:35:31,717 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:35:31,717 - INFO - [步骤 5/5] 开始爬取 '雨花司法' 的文章...
2025-08-04 16:35:31,842 - WARNING - ⚠️ 公众号 '雨花司法' 未获取到任何文章数据
2025-08-04 16:35:31,843 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:35:46,843 - INFO - ============================================================
2025-08-04 16:35:46,844 - INFO - 📍 处理第 3/4 个公众号: 江宁司法
2025-08-04 16:35:46,844 - INFO - ============================================================
2025-08-04 16:35:46,844 - INFO - [步骤 1/5] 为 '江宁司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:35:46,844 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:35:46,844 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:35:46,845 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:35:46,845 - INFO - === 开始重置网络状态 ===
2025-08-04 16:35:46,845 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:35:46,967 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:35:46,967 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:35:46,967 - INFO - 系统代理已成功关闭
2025-08-04 16:35:46,968 - INFO - ✅ 代理关闭操作
2025-08-04 16:35:46,968 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:35:48,275 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:48,276 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:35:48,276 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:35:48,276 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:35:48,277 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:35:48,775 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:35:48,776 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32896
2025-08-04 16:35:48,776 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:35:51,777 - INFO - 等待代理服务启动...
2025-08-04 16:35:52,641 - INFO - 代理服务已启动并正常工作
2025-08-04 16:35:52,641 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32896)
2025-08-04 16:35:52,642 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:35:52,642 - INFO - [步骤 2/5] 为 '江宁司法' 启动 UI 自动化...
2025-08-04 16:35:52,643 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:35:52,643 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:35:52,644 - INFO - 正在查找微信主窗口...
2025-08-04 16:35:53,131 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:35:53,131 - INFO - 正在激活微信窗口...
2025-08-04 16:35:55,643 - INFO - 微信窗口已激活。
2025-08-04 16:35:55,644 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:36:02,353 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:36:02,353 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:36:05,687 - INFO - 正在查找聊天输入框...
2025-08-04 16:36:07,689 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:36:07,699 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:36:07,699 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:36:09,289 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&mid=2652140303&idx=1&sn=74a10900f46e96be6567975b1d1ccd59&chksm=bda865f78adfece121715d85d10271b02fb19ce45ba1cfd5135e99209e0775ba3a82914ee28b#rd
2025-08-04 16:36:11,578 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:36:11,810 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:36:12,621 - INFO - 链接已发送。
2025-08-04 16:36:15,623 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:36:17,712 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:36:18,435 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:36:21,436 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:36:21,437 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:36:21,438 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:36:21,445 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:36:24,150 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:36:25,652 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:36:32,999 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:36:32,999 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:36:32,999 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:36:32,999 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:36:33,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:33,000 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:36:33,000 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:36:33,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:33,000 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:36:33,000 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:36:33,000 - INFO - [步骤 3/5] 等待 '江宁司法' 的 Cookie 数据...
2025-08-04 16:36:33,000 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:36:34,001 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:36:34,002 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:34,003 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:36:34,003 - INFO - [步骤 4/5] 停止 '江宁司法' 的 Cookie 抓取器...
2025-08-04 16:36:34,003 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:36:34,004 - INFO - 正在停止Cookie抓取器 (PID: 32896)...
2025-08-04 16:36:34,006 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:36:34,006 - INFO - 正在验证并清理代理设置...
2025-08-04 16:36:34,007 - INFO - === 开始重置网络状态 ===
2025-08-04 16:36:34,007 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:36:34,131 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:36:34,131 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:36:34,131 - INFO - 系统代理已成功关闭
2025-08-04 16:36:34,131 - INFO - ✅ 代理关闭操作
2025-08-04 16:36:34,131 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:36:35,700 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:35,700 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:36:35,701 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:36:36,620 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:36,621 - INFO - ✅ 网络连接验证正常
2025-08-04 16:36:39,622 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:36:39,623 - INFO - [步骤 5/5] 开始爬取 '江宁司法' 的文章...
2025-08-04 16:36:39,731 - WARNING - ⚠️ 公众号 '江宁司法' 未获取到任何文章数据
2025-08-04 16:36:39,732 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:36:54,732 - INFO - ============================================================
2025-08-04 16:36:54,733 - INFO - 📍 处理第 4/4 个公众号: 浦口普法
2025-08-04 16:36:54,734 - INFO - ============================================================
2025-08-04 16:36:54,736 - INFO - [步骤 1/5] 为 '浦口普法' 创建独立的 Cookie 抓取器...
2025-08-04 16:36:54,737 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:36:54,738 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:36:54,738 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:36:54,739 - INFO - === 开始重置网络状态 ===
2025-08-04 16:36:54,739 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:36:54,854 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:36:54,854 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:36:54,854 - INFO - 系统代理已成功关闭
2025-08-04 16:36:54,854 - INFO - ✅ 代理关闭操作
2025-08-04 16:36:54,855 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:36:57,354 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:57,354 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:36:57,354 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:36:57,355 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:36:57,355 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:36:57,849 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:36:57,850 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21332
2025-08-04 16:36:57,851 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:37:00,851 - INFO - 等待代理服务启动...
2025-08-04 16:37:06,311 - INFO - 代理服务已启动并正常工作
2025-08-04 16:37:06,311 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21332)
2025-08-04 16:37:06,312 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:37:06,312 - INFO - [步骤 2/5] 为 '浦口普法' 启动 UI 自动化...
2025-08-04 16:37:06,313 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:37:06,314 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:37:06,314 - INFO - 正在查找微信主窗口...
2025-08-04 16:37:06,548 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:37:06,548 - INFO - 正在激活微信窗口...
2025-08-04 16:37:09,064 - INFO - 微信窗口已激活。
2025-08-04 16:37:09,066 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:37:15,788 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:37:15,789 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:37:19,114 - INFO - 正在查找聊天输入框...
2025-08-04 16:37:21,116 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:37:21,128 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:37:21,129 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:37:22,692 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&mid=2452706749&idx=1&sn=b59ee1711a93713adc69fbbdc9b557c6&chksm=8cef28c6bb98a1d09cc437733de4ca183767c6e852a2fba8c0a86e699900e8cda2f1280fbac5#rd
2025-08-04 16:37:24,981 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:37:25,210 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:37:26,018 - INFO - 链接已发送。
2025-08-04 16:37:29,020 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:37:31,113 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:37:31,835 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:37:34,836 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:37:34,837 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:37:34,837 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:37:34,847 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:37:37,553 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:37:39,054 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:37:46,399 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:37:46,399 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:37:46,399 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:37:46,400 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:37:46,400 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:46,400 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:37:46,400 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:37:46,400 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:46,400 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:37:46,400 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:37:46,401 - INFO - [步骤 3/5] 等待 '浦口普法' 的 Cookie 数据...
2025-08-04 16:37:46,401 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:37:47,401 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:37:47,402 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:47,403 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:37:47,403 - INFO - [步骤 4/5] 停止 '浦口普法' 的 Cookie 抓取器...
2025-08-04 16:37:47,404 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:37:47,404 - INFO - 正在停止Cookie抓取器 (PID: 21332)...
2025-08-04 16:37:47,406 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:37:47,407 - INFO - 正在验证并清理代理设置...
2025-08-04 16:37:47,407 - INFO - === 开始重置网络状态 ===
2025-08-04 16:37:47,407 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:37:47,502 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:37:47,502 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:37:47,502 - INFO - 系统代理已成功关闭
2025-08-04 16:37:47,502 - INFO - ✅ 代理关闭操作
2025-08-04 16:37:47,502 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:37:48,439 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:37:48,440 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:37:48,440 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:37:49,390 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:37:49,390 - INFO - ✅ 网络连接验证正常
2025-08-04 16:37:52,391 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:37:52,392 - INFO - [步骤 5/5] 开始爬取 '浦口普法' 的文章...
2025-08-04 16:37:52,511 - WARNING - ⚠️ 公众号 '浦口普法' 未获取到任何文章数据
2025-08-04 16:37:52,511 - INFO - ================================================================================
2025-08-04 16:37:52,511 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 16:37:52,511 - INFO - ================================================================================
2025-08-04 16:37:52,512 - INFO - ✅ 成功处理: 0 个公众号
2025-08-04 16:37:52,512 - INFO - ❌ 失败处理: 4 个公众号
2025-08-04 16:37:52,512 - INFO - 📄 总计文章: 0 篇
2025-08-04 16:37:52,513 - INFO - ================================================================================
2025-08-04 16:37:52,513 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 16:37:52,514 - INFO - ================================================================================
