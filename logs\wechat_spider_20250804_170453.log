2025-08-04 17:04:53,254 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:04:53,254 - INFO - ================================================================================
2025-08-04 17:04:53,254 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:04:53,254 - INFO - ================================================================================
2025-08-04 17:04:53,254 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:04:53,331 - INFO - 找到有效目标 1: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 2: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 3: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 4: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-04 17:04:53,333 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:04:53,333 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:04:53,333 - INFO - ============================================================
2025-08-04 17:04:53,333 - INFO - 📍 处理第 1/4 个公众号: 南京工会会员
2025-08-04 17:04:53,333 - INFO - ============================================================
2025-08-04 17:04:53,333 - INFO - [步骤 1/5] 为 '南京工会会员' 创建独立的 Cookie 抓取器...
2025-08-04 17:04:53,333 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:04:53,333 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:04:53,333 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:04:53,333 - INFO - === 开始重置网络状态 ===
2025-08-04 17:04:53,333 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:04:53,454 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:04:53,455 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:04:53,455 - INFO - 系统代理已成功关闭
2025-08-04 17:04:53,455 - INFO - ✅ 代理关闭操作
2025-08-04 17:04:53,455 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:04:57,597 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:04:57,598 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:04:57,598 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:04:57,598 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:04:57,599 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:04:58,101 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:04:58,102 - INFO - 🔄 Cookie抓取器进程已启动，PID: 2404
2025-08-04 17:04:58,102 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:05:01,102 - INFO - 等待代理服务启动...
2025-08-04 17:05:01,688 - INFO - 代理服务已启动并正常工作
2025-08-04 17:05:01,688 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 2404)
2025-08-04 17:05:01,689 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:05:01,689 - INFO - [步骤 2/5] 为 '南京工会会员' 启动 UI 自动化...
2025-08-04 17:05:01,689 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:05:01,689 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:05:01,689 - INFO - 正在查找微信主窗口...
2025-08-04 17:05:03,003 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:05:03,003 - INFO - 正在激活微信窗口...
2025-08-04 17:05:05,519 - INFO - 微信窗口已激活。
2025-08-04 17:05:05,520 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:05:12,336 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:05:12,337 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:05:15,671 - INFO - 正在查找聊天输入框...
2025-08-04 17:05:17,672 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:05:17,687 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:05:17,688 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:05:19,291 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&mid=2247667614&idx=1&sn=4488f6e1afcbdb1b49eec9f270562f5e&chksm=ec4b9db7db3c14a1943b9f0e52b6650c218685563b802583089ce84c48d54580c69e663b934c#rd
2025-08-04 17:05:21,585 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:05:21,832 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:05:22,638 - INFO - 链接已发送。
2025-08-04 17:05:25,639 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:05:27,739 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:05:28,472 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:05:31,473 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:05:31,474 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:05:31,474 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:05:31,478 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:05:34,184 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:05:35,685 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:05:43,081 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:05:43,081 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:05:43,081 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:05:43,082 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:05:43,082 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:43,083 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:05:43,083 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:05:43,083 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:43,083 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:05:43,083 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:05:43,083 - INFO - [步骤 3/5] 等待 '南京工会会员' 的 Cookie 数据...
2025-08-04 17:05:43,083 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:05:44,084 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:05:44,085 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:44,086 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:05:44,086 - INFO - [步骤 4/5] 停止 '南京工会会员' 的 Cookie 抓取器...
2025-08-04 17:05:44,087 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:05:44,087 - INFO - 正在停止Cookie抓取器 (PID: 2404)...
2025-08-04 17:05:44,089 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:05:44,090 - INFO - 正在验证并清理代理设置...
2025-08-04 17:05:44,090 - INFO - === 开始重置网络状态 ===
2025-08-04 17:05:44,090 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:05:44,231 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:05:44,231 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:05:44,231 - INFO - 系统代理已成功关闭
2025-08-04 17:05:44,231 - INFO - ✅ 代理关闭操作
2025-08-04 17:05:44,232 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:05:45,167 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:05:45,168 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:05:45,169 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:05:46,118 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:05:46,119 - INFO - ✅ 网络连接验证正常
2025-08-04 17:05:49,119 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:05:49,120 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:06:04,121 - INFO - [步骤 5/5] 开始爬取 '南京工会会员' 的文章...
2025-08-04 17:06:04,121 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:06:04,256 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:06:14,256 - INFO - 🔄 尝试第 2/3 次爬取...
2025-08-04 17:06:14,444 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:06:24,445 - INFO - 🔄 尝试第 3/3 次爬取...
2025-08-04 17:06:24,574 - ERROR - ❌ 所有重试都失败了
2025-08-04 17:06:24,574 - WARNING - ⚠️ 公众号 '南京工会会员' 未获取到任何文章数据
2025-08-04 17:06:24,575 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 17:06:39,575 - INFO - ============================================================
2025-08-04 17:06:39,576 - INFO - 📍 处理第 2/4 个公众号: 青春南京
2025-08-04 17:06:39,576 - INFO - ============================================================
2025-08-04 17:06:39,577 - INFO - [步骤 1/5] 为 '青春南京' 创建独立的 Cookie 抓取器...
2025-08-04 17:06:39,578 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:06:39,579 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:06:39,579 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:06:39,579 - INFO - === 开始重置网络状态 ===
2025-08-04 17:06:39,580 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:06:39,684 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:06:39,684 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:06:39,684 - INFO - 系统代理已成功关闭
2025-08-04 17:06:39,684 - INFO - ✅ 代理关闭操作
2025-08-04 17:06:39,685 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:06:42,586 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:06:42,587 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:06:42,587 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:06:42,588 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:06:42,589 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:06:43,097 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:06:43,097 - INFO - 🔄 Cookie抓取器进程已启动，PID: 18748
2025-08-04 17:06:43,098 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:06:46,098 - INFO - 等待代理服务启动...
2025-08-04 17:06:46,578 - INFO - 代理服务已启动并正常工作
2025-08-04 17:06:46,579 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 18748)
2025-08-04 17:06:46,579 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:06:46,580 - INFO - [步骤 2/5] 为 '青春南京' 启动 UI 自动化...
2025-08-04 17:06:46,581 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:06:46,582 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:06:46,582 - INFO - 正在查找微信主窗口...
2025-08-04 17:06:46,978 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:06:46,978 - INFO - 正在激活微信窗口...
2025-08-04 17:06:49,491 - INFO - 微信窗口已激活。
2025-08-04 17:06:49,491 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:06:56,209 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:06:56,210 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:06:59,538 - INFO - 正在查找聊天输入框...
2025-08-04 17:07:01,538 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:07:01,549 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:07:01,549 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:07:03,118 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&mid=2650568247&idx=1&sn=f122dfa78fd5d79d0413c48c7f2f664a&chksm=be88323589ffbb23a3b8693c03b2867b211309e68758c5f85d3e15f4dc8dba0d5480a3412abc#rd
2025-08-04 17:07:05,408 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:07:05,656 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:07:06,471 - INFO - 链接已发送。
2025-08-04 17:07:09,472 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:07:11,567 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:07:12,304 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:07:15,305 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:07:15,306 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:07:15,306 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:07:15,311 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:07:18,016 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:07:19,518 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:07:26,896 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:07:26,897 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:07:26,897 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:07:26,897 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:07:26,897 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:26,897 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:07:26,897 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:07:26,897 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:26,898 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:07:26,898 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:07:26,898 - INFO - [步骤 3/5] 等待 '青春南京' 的 Cookie 数据...
2025-08-04 17:07:26,898 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:07:27,898 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:07:27,900 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:27,901 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:07:27,902 - INFO - [步骤 4/5] 停止 '青春南京' 的 Cookie 抓取器...
2025-08-04 17:07:27,902 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:07:27,903 - INFO - 正在停止Cookie抓取器 (PID: 18748)...
2025-08-04 17:07:27,905 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:07:27,905 - INFO - 正在验证并清理代理设置...
2025-08-04 17:07:27,906 - INFO - === 开始重置网络状态 ===
2025-08-04 17:07:27,906 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:07:28,005 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:07:28,005 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:07:28,005 - INFO - 系统代理已成功关闭
2025-08-04 17:07:28,005 - INFO - ✅ 代理关闭操作
2025-08-04 17:07:28,005 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:07:31,056 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:07:31,056 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:07:31,056 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:07:31,945 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:07:31,946 - INFO - ✅ 网络连接验证正常
2025-08-04 17:07:34,946 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:07:34,947 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:07:49,948 - INFO - [步骤 5/5] 开始爬取 '青春南京' 的文章...
2025-08-04 17:07:49,949 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:07:50,058 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:08:00,059 - INFO - 🔄 尝试第 2/3 次爬取...
2025-08-04 17:08:00,164 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:08:10,165 - INFO - 🔄 尝试第 3/3 次爬取...
2025-08-04 17:08:10,353 - ERROR - ❌ 所有重试都失败了
2025-08-04 17:08:10,354 - WARNING - ⚠️ 公众号 '青春南京' 未获取到任何文章数据
2025-08-04 17:08:10,354 - INFO - ⏳ 公众号间延迟 15 秒...
