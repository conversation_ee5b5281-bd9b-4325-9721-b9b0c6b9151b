#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比两次Cookie抓取的差异
"""
import os
import re
from datetime import datetime

def parse_cookie_file(file_path):
    """解析Cookie文件，返回所有记录"""
    if not os.path.exists(file_path):
        return []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    records = []
    sections = content.split('=' * 60)
    
    for section in sections:
        if 'time:' in section and 'Cookies:' in section:
            record = {}
            lines = section.strip().split('\n')
            
            current_section = None
            for line in lines:
                line = line.strip()
                if line.startswith('time:'):
                    record['time'] = line.replace('time:', '').strip()
                elif line.startswith('allurl:'):
                    record['url'] = line.replace('allurl:', '').strip()
                elif line.startswith('Cookies:'):
                    record['cookies'] = line.replace('Cookies:', '').strip()
                elif line == 'Headers:':
                    current_section = 'headers'
                    record['headers'] = {}
                elif line == 'Cookie_Details:':
                    current_section = 'cookie_details'
                    record['cookie_details'] = {}
                elif line == 'All_Headers:':
                    current_section = 'all_headers'
                    record['all_headers'] = {}
                elif line.startswith('  ') and current_section:
                    if ':' in line:
                        key, value = line.strip().split(':', 1)
                        record[current_section][key.strip()] = value.strip()
            
            if 'time' in record:
                records.append(record)
    
    return records

def compare_cookies(record1, record2):
    """对比两个Cookie记录的差异"""
    print(f"\n🔍 对比分析")
    print("=" * 60)
    
    # 时间对比
    print(f"📅 第一次时间: {record1.get('time', 'N/A')}")
    print(f"📅 第二次时间: {record2.get('time', 'N/A')}")
    
    # URL对比
    url1 = record1.get('url', '')
    url2 = record2.get('url', '')
    if url1 == url2:
        print("✅ URL相同")
    else:
        print("❌ URL不同")
        print(f"   第一次: {url1[:100]}...")
        print(f"   第二次: {url2[:100]}...")
    
    # Cookie字符串对比
    cookies1 = record1.get('cookies', '')
    cookies2 = record2.get('cookies', '')
    if cookies1 == cookies2:
        print("✅ Cookie字符串完全相同")
    else:
        print("❌ Cookie字符串不同")
        print(f"   长度差异: {len(cookies1)} vs {len(cookies2)}")
    
    # 详细Cookie对比
    details1 = record1.get('cookie_details', {})
    details2 = record2.get('cookie_details', {})
    
    print(f"\n🍪 Cookie详细对比:")
    all_keys = set(details1.keys()) | set(details2.keys())
    
    for key in sorted(all_keys):
        val1 = details1.get(key, '[缺失]')
        val2 = details2.get(key, '[缺失]')
        
        if val1 == val2:
            print(f"✅ {key}: 相同")
        else:
            print(f"❌ {key}: 不同")
            print(f"   第一次: {val1[:50]}...")
            print(f"   第二次: {val2[:50]}...")
    
    # 关键字段对比
    print(f"\n🔑 关键字段对比:")
    key_fields = ['appmsg_token', 'pass_ticket', 'wap_sid2', 'wxuin']
    
    for field in key_fields:
        val1 = details1.get(field, '[缺失]')
        val2 = details2.get(field, '[缺失]')
        
        if val1 == val2:
            print(f"✅ {field}: 相同")
        else:
            print(f"❌ {field}: 不同")
            if val1 != '[缺失]' and val2 != '[缺失]':
                print(f"   第一次: {val1}")
                print(f"   第二次: {val2}")

def main():
    """主函数"""
    print("🔍 Cookie抓取对比分析工具")
    print("=" * 60)
    
    # 解析Cookie文件
    records = parse_cookie_file('wechat_keys.txt')
    
    if len(records) < 2:
        print(f"❌ 需要至少2条记录进行对比，当前只有 {len(records)} 条")
        print("💡 请先运行两次抓取，然后再运行此工具")
        return
    
    print(f"📊 找到 {len(records)} 条Cookie记录")
    
    # 显示所有记录的时间
    for i, record in enumerate(records):
        print(f"   {i+1}. {record.get('time', 'N/A')}")
    
    # 对比最后两次记录
    if len(records) >= 2:
        print(f"\n🔄 对比最后两次抓取:")
        compare_cookies(records[-2], records[-1])
    
    # 如果有多条记录，也可以对比第一次和最后一次
    if len(records) > 2:
        print(f"\n🔄 对比第一次和最后一次抓取:")
        compare_cookies(records[0], records[-1])

if __name__ == "__main__":
    main()
