2025-08-02 21:25:36,865 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 21:25:36,866 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-02 21:25:36,878 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-02 21:25:36,878 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 21:25:36,879 - INFO - ================================================================================
2025-08-02 21:25:36,879 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-02 21:25:36,880 - INFO - ================================================================================
2025-08-02 21:25:36,880 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-02 21:25:36,881 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-02 21:25:41,933 - INFO - Cookie抓取器已在后台运行 (PID: 30208)。
2025-08-02 21:25:41,934 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-02 21:25:41,935 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-02 21:25:41,936 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 21:25:41,937 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:25:41,939 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-02 21:25:42,437 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-02 21:25:42,438 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-02 21:25:42,438 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-02 21:25:42,439 - INFO - 准备将链接发送到文件传输助手...
2025-08-02 21:25:42,439 - INFO - 正在查找微信主窗口...
2025-08-02 21:25:42,722 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-02 21:25:42,722 - INFO - 正在激活微信窗口...
2025-08-02 21:25:45,262 - INFO - 微信窗口已激活。
2025-08-02 21:25:45,262 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-02 21:25:52,608 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-02 21:25:52,608 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-02 21:25:56,026 - INFO - 正在查找聊天输入框...
2025-08-02 21:25:58,036 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-02 21:25:58,043 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-02 21:25:58,044 - INFO - 点击聊天输入区域坐标: (430, 923)
2025-08-02 21:25:59,623 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-02 21:26:01,973 - INFO - 链接已粘贴，正在发送...
2025-08-02 21:26:02,362 - INFO - 找到发送按钮，点击发送...
2025-08-02 21:26:03,266 - INFO - 链接已发送。
2025-08-02 21:26:06,281 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-02 21:26:08,474 - INFO - 已定位到最新的消息项，准备点击。
2025-08-02 21:26:09,277 - INFO - ✅ 成功点击最新链接。
2025-08-02 21:26:09,279 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-02 21:26:09,280 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-02 21:26:09,280 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-02 21:26:09,281 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-02 21:27:12,893 - INFO - 检测到Cookie文件已生成。
2025-08-02 21:27:12,895 - INFO - 从文件中解析到有效Cookie数据。
2025-08-02 21:27:12,895 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-02 21:27:12,896 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-02 21:27:12,896 - INFO - Cookie抓取器未在运行或已停止。
2025-08-02 21:27:15,906 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-02 21:27:15,906 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-02 21:27:15,907 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 21:27:15,908 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:27:15,908 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:27:15,909 - WARNING - ⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。
2025-08-02 21:27:15,909 - INFO - ================================================================================
2025-08-02 21:27:15,909 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-02 21:27:15,910 - INFO - ================================================================================
2025-08-02 21:27:30,944 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 21:27:30,944 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-02 21:27:30,952 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-02 21:27:30,952 - INFO - ================================================================================
2025-08-02 21:27:30,952 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-02 21:27:30,953 - INFO - ================================================================================
2025-08-02 21:27:30,953 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-02 21:27:30,954 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-02 21:27:36,007 - INFO - Cookie抓取器已在后台运行 (PID: 9484)。
2025-08-02 21:27:36,007 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-02 21:27:36,007 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-02 21:27:36,008 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 21:27:36,009 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:27:36,009 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-02 21:27:36,555 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-02 21:27:36,556 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-02 21:27:36,556 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-02 21:27:36,557 - INFO - 准备将链接发送到文件传输助手...
2025-08-02 21:27:36,557 - INFO - 正在查找微信主窗口...
2025-08-02 21:27:36,881 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-02 21:27:36,882 - INFO - 正在激活微信窗口...
2025-08-02 21:27:39,435 - INFO - 微信窗口已激活。
2025-08-02 21:27:39,435 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-02 21:27:46,829 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-02 21:27:46,830 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-02 21:27:50,224 - INFO - 正在查找聊天输入框...
2025-08-02 21:27:52,233 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-02 21:27:52,240 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-02 21:27:52,241 - INFO - 点击聊天输入区域坐标: (430, 923)
2025-08-02 21:27:53,847 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-02 21:27:56,186 - INFO - 链接已粘贴，正在发送...
2025-08-02 21:27:56,599 - INFO - 找到发送按钮，点击发送...
2025-08-02 21:27:57,534 - INFO - 链接已发送。
2025-08-02 21:28:00,547 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-02 21:28:02,741 - INFO - 已定位到最新的消息项，准备点击。
2025-08-02 21:28:03,564 - INFO - ✅ 成功点击最新链接。
2025-08-02 21:28:03,565 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-02 21:28:03,566 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-02 21:28:03,566 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-02 21:28:03,567 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-02 21:28:19,708 - INFO - 检测到Cookie文件已生成。
2025-08-02 21:28:19,711 - INFO - 从文件中解析到有效Cookie数据。
2025-08-02 21:28:19,712 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-02 21:28:19,713 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-02 21:28:19,714 - INFO - Cookie抓取器未在运行或已停止。
2025-08-02 21:28:22,723 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-02 21:28:22,723 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-02 21:28:22,723 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 21:28:22,724 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:28:22,726 - WARNING - 文件 wechat_keys.txt 不存在
2025-08-02 21:28:22,726 - WARNING - ⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。
2025-08-02 21:28:22,726 - INFO - ================================================================================
2025-08-02 21:28:22,727 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-02 21:28:22,727 - INFO - ================================================================================
2025-08-02 22:37:21,321 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 22:37:21,321 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-02 22:37:21,341 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-02 22:37:21,342 - INFO - ================================================================================
2025-08-02 22:37:21,343 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-02 22:37:21,343 - INFO - ================================================================================
2025-08-02 22:37:21,344 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-02 22:37:21,345 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-02 22:37:26,398 - INFO - Cookie抓取器已在后台运行 (PID: 21108)。
2025-08-02 22:37:26,399 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-02 22:37:26,399 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-02 22:37:26,400 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 22:37:26,401 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-02 22:37:26,933 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-02 22:37:26,934 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-02 22:37:26,934 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-02 22:37:26,934 - INFO - 准备将链接发送到文件传输助手...
2025-08-02 22:37:26,935 - INFO - 正在查找微信主窗口...
2025-08-02 22:37:27,145 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-02 22:37:27,145 - INFO - 正在激活微信窗口...
2025-08-02 22:37:29,697 - INFO - 微信窗口已激活。
2025-08-02 22:37:29,697 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-02 22:37:37,146 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-02 22:37:37,147 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-02 22:37:40,563 - INFO - 正在查找聊天输入框...
2025-08-02 22:37:42,576 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-02 22:37:42,583 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-02 22:37:42,584 - INFO - 点击聊天输入区域坐标: (430, 923)
2025-08-02 22:37:44,182 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-02 22:37:46,523 - INFO - 链接已粘贴，正在发送...
2025-08-02 22:37:46,930 - INFO - 找到发送按钮，点击发送...
2025-08-02 22:37:47,863 - INFO - 链接已发送。
2025-08-02 22:37:50,878 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-02 22:37:53,080 - INFO - 已定位到最新的消息项，准备点击。
2025-08-02 22:37:53,909 - INFO - ✅ 成功点击最新链接。
2025-08-02 22:37:53,911 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-02 22:37:53,911 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-02 22:37:53,911 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-02 22:37:53,911 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-02 22:38:15,108 - INFO - 检测到Cookie文件已生成。
2025-08-02 22:38:15,108 - INFO - 从文件中解析到有效Cookie数据。
2025-08-02 22:38:15,109 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-02 22:38:15,109 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-02 22:38:15,110 - INFO - Cookie抓取器未在运行或已停止。
2025-08-02 22:38:18,111 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-02 22:38:18,111 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-02 22:38:18,113 - ERROR - ❌ 自动化流程发生未知严重错误: __init__() got an unexpected keyword argument 'auth_info'
2025-08-02 22:38:18,113 - ERROR - Traceback (most recent call last):
  File "E:\mynj\wechat_spider\automated_crawler.py", line 78, in run
    batch_spider = BatchReadnumSpider(auth_info=auth_info)
TypeError: __init__() got an unexpected keyword argument 'auth_info'

2025-08-02 22:38:18,113 - INFO - 正在尝试清理资源...
2025-08-02 22:38:18,114 - INFO - Cookie抓取器未在运行或已停止。
2025-08-02 22:42:13,067 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 22:42:13,067 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-02 22:42:13,077 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-02 22:42:13,078 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 22:42:13,078 - INFO - ================================================================================
2025-08-02 22:42:13,079 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-02 22:42:13,079 - INFO - ================================================================================
2025-08-02 22:42:13,080 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-02 22:42:13,091 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-02 22:42:18,164 - INFO - Cookie抓取器已在后台运行 (PID: 30348)。
2025-08-02 22:42:18,164 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-02 22:42:18,165 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-02 22:42:18,166 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-02 22:42:18,166 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-02 22:42:18,744 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-02 22:42:18,745 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-02 22:42:18,745 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-02 22:42:18,746 - INFO - 准备将链接发送到文件传输助手...
2025-08-02 22:42:18,746 - INFO - 正在查找微信主窗口...
2025-08-02 22:42:19,007 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-02 22:42:19,007 - INFO - 正在激活微信窗口...
2025-08-02 22:42:21,539 - INFO - 微信窗口已激活。
2025-08-02 22:42:21,539 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-02 22:42:29,025 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-02 22:42:29,026 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-02 22:42:32,460 - INFO - 正在查找聊天输入框...
2025-08-02 22:42:34,473 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-02 22:42:34,480 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-02 22:42:34,481 - INFO - 点击聊天输入区域坐标: (430, 923)
2025-08-02 22:42:36,084 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-02 22:42:38,456 - INFO - 链接已粘贴，正在发送...
2025-08-02 22:42:38,986 - INFO - 找到发送按钮，点击发送...
2025-08-02 22:42:39,899 - INFO - 链接已发送。
2025-08-02 22:42:42,913 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-02 22:42:45,160 - INFO - 已定位到最新的消息项，准备点击。
2025-08-02 22:42:45,966 - INFO - ✅ 成功点击最新链接。
2025-08-02 22:42:45,968 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-02 22:42:45,968 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-02 22:42:45,969 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-02 22:42:45,969 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-02 22:43:00,126 - INFO - 检测到Cookie文件已生成。
2025-08-02 22:43:00,128 - INFO - 从文件中解析到有效Cookie数据。
2025-08-02 22:43:00,129 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-02 22:43:00,130 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-02 22:43:00,131 - INFO - Cookie抓取器未在运行或已停止。
2025-08-02 22:43:03,139 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-02 22:43:03,139 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-03 23:17:24,826 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:17:24,827 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:17:24,834 - INFO - 开始测试微信浏览器自动化模块...
2025-08-03 23:17:24,834 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:17:24,835 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:17:24,835 - INFO - 正在查找微信主窗口...
2025-08-03 23:17:37,541 - WARNING - 未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...
2025-08-03 23:17:37,546 - INFO - 成功找到微信窗口 (Name='微信')
2025-08-03 23:17:37,547 - INFO - 正在激活微信窗口...
2025-08-03 23:17:40,086 - INFO - 微信窗口已激活。
2025-08-03 23:17:40,086 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:17:47,142 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:17:47,142 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:17:50,559 - INFO - 正在查找聊天输入框...
2025-08-03 23:17:52,574 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:17:52,583 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:17:52,584 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:17:54,217 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-03 23:17:56,589 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:17:57,171 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:17:58,102 - INFO - 链接已发送。
2025-08-03 23:18:01,111 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:18:03,370 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:18:04,186 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:18:04,187 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:18:06,194 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:18:07,725 - INFO - 已发送F5刷新指令
2025-08-03 23:18:07,725 - INFO - 等待页面刷新完成... (3.0秒)
2025-08-03 23:18:10,736 - INFO - ✅ 自动刷新操作完成
2025-08-03 23:25:30,509 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:25:30,510 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:25:30,516 - INFO - 开始测试微信浏览器自动化模块...
2025-08-03 23:25:30,517 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:25:30,517 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:25:30,518 - INFO - 正在查找微信主窗口...
2025-08-03 23:25:35,316 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-03 23:25:35,316 - INFO - 正在激活微信窗口...
2025-08-03 23:25:37,910 - INFO - 微信窗口已激活。
2025-08-03 23:25:37,910 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:25:45,072 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:25:45,073 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:25:48,476 - INFO - 正在查找聊天输入框...
2025-08-03 23:25:50,481 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:25:50,490 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:25:50,491 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:25:52,131 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s/0rtqSdjV9rApEHhFqC9gLw
2025-08-03 23:25:54,492 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:25:55,093 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:25:56,016 - INFO - 链接已发送。
2025-08-03 23:25:59,020 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:26:01,284 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:26:02,082 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:26:02,082 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:26:04,097 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:26:05,623 - INFO - 已发送F5刷新指令
2025-08-03 23:26:05,623 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:26:08,135 - INFO - 重新激活微信主窗口...
2025-08-03 23:26:08,265 - INFO - 正在激活微信窗口...
2025-08-03 23:26:10,814 - INFO - 微信窗口已激活。
2025-08-03 23:26:11,826 - INFO - 正在执行第 2 次刷新操作...
2025-08-03 23:26:13,355 - INFO - 已发送F5刷新指令
2025-08-03 23:26:13,355 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:26:15,855 - INFO - 重新激活微信主窗口...
2025-08-03 23:26:16,082 - INFO - 正在激活微信窗口...
2025-08-03 23:26:18,634 - INFO - 微信窗口已激活。
2025-08-03 23:26:28,628 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:26:28,630 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:26:28,640 - INFO - 开始测试微信浏览器自动化模块...
2025-08-03 23:26:28,643 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:26:28,644 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:26:28,644 - INFO - 正在查找微信主窗口...
2025-08-03 23:26:31,505 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-03 23:26:31,507 - INFO - 正在激活微信窗口...
2025-08-03 23:26:34,050 - INFO - 微信窗口已激活。
2025-08-03 23:26:34,050 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:26:40,848 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:26:40,848 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:26:44,249 - INFO - 正在查找聊天输入框...
2025-08-03 23:26:46,251 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:26:46,260 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:26:46,260 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:26:47,876 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-03 23:26:50,232 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:26:50,852 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:26:51,780 - INFO - 链接已发送。
2025-08-03 23:26:54,794 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:26:57,038 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:26:57,865 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:26:57,865 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:26:59,876 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:27:01,403 - INFO - 已发送F5刷新指令
2025-08-03 23:27:01,403 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:27:03,908 - INFO - 重新激活微信主窗口...
2025-08-03 23:27:05,076 - INFO - 正在激活微信窗口...
2025-08-03 23:27:07,623 - INFO - 微信窗口已激活。
2025-08-03 23:27:08,638 - INFO - 正在执行第 2 次刷新操作...
2025-08-03 23:27:10,165 - INFO - 已发送F5刷新指令
2025-08-03 23:27:10,165 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:27:12,676 - INFO - 重新激活微信主窗口...
2025-08-03 23:27:12,814 - INFO - 正在激活微信窗口...
2025-08-03 23:27:15,365 - INFO - 微信窗口已激活。
2025-08-03 23:27:16,365 - INFO - 正在执行第 3 次刷新操作...
2025-08-03 23:27:17,883 - INFO - 已发送F5刷新指令
2025-08-03 23:27:17,884 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:27:20,387 - INFO - ✅ 自动刷新操作完成
2025-08-03 23:42:26,649 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:42:26,649 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:42:26,656 - INFO - 开始测试微信浏览器自动化模块...
2025-08-03 23:42:26,657 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:42:26,657 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:42:26,657 - INFO - 正在查找微信主窗口...
2025-08-03 23:42:29,194 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-03 23:42:29,195 - INFO - 正在激活微信窗口...
2025-08-03 23:42:31,734 - INFO - 微信窗口已激活。
2025-08-03 23:42:31,735 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:42:38,811 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:42:38,811 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:42:42,238 - INFO - 正在查找聊天输入框...
2025-08-03 23:42:44,249 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:42:44,258 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:42:44,259 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:42:45,874 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-03 23:42:48,252 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:42:48,901 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:42:49,824 - INFO - 链接已发送。
2025-08-03 23:42:52,837 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:42:55,099 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:42:55,912 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:42:55,913 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:42:58,921 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:42:58,921 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:42:58,921 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:42:58,930 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:43:01,713 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:43:02,742 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:43:02,742 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:43:05,254 - INFO - 第 1 次刷新完成
2025-08-03 23:43:06,761 - INFO - 正在执行第 2 次刷新操作...
2025-08-03 23:43:06,761 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:43:06,761 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:43:06,770 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:43:09,534 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:43:10,563 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:43:10,563 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:43:13,070 - INFO - 第 2 次刷新完成
2025-08-03 23:43:14,585 - INFO - 正在执行第 3 次刷新操作...
2025-08-03 23:43:14,586 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:43:14,586 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:43:14,593 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:43:17,360 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:43:18,387 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:43:18,387 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:43:20,889 - INFO - 第 3 次刷新完成
2025-08-03 23:43:20,889 - INFO - ✅ 自动刷新操作全部完成
2025-08-03 23:49:37,994 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:49:37,994 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:49:38,002 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-03 23:49:38,003 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-03 23:49:38,004 - INFO - ================================================================================
2025-08-03 23:49:38,004 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-03 23:49:38,004 - INFO - ================================================================================
2025-08-03 23:49:38,005 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-03 23:49:38,014 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-03 23:49:43,069 - INFO - Cookie抓取器已在后台运行 (PID: 23472)。
2025-08-03 23:49:43,069 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-03 23:49:43,069 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-03 23:49:43,070 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-03 23:49:43,071 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-03 23:49:43,598 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-03 23:49:43,599 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-03 23:49:43,599 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:49:43,600 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:49:43,600 - INFO - 正在查找微信主窗口...
2025-08-03 23:49:43,836 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-03 23:49:43,837 - INFO - 正在激活微信窗口...
2025-08-03 23:49:46,391 - INFO - 微信窗口已激活。
2025-08-03 23:49:46,392 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:49:53,351 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:49:53,351 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:49:56,746 - INFO - 正在查找聊天输入框...
2025-08-03 23:49:58,758 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:49:58,765 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:49:58,765 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:50:00,373 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-03 23:50:02,738 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:50:03,292 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:50:04,198 - INFO - 链接已发送。
2025-08-03 23:50:07,212 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:50:09,459 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:50:10,289 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:50:10,289 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:50:13,298 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:50:13,299 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:50:13,299 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:50:13,307 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:50:16,073 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:50:17,104 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:50:17,105 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:50:19,606 - INFO - 第 1 次刷新完成
2025-08-03 23:50:21,121 - INFO - 正在执行第 2 次刷新操作...
2025-08-03 23:50:21,121 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:50:21,121 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:50:21,129 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:50:23,901 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:50:24,925 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:50:24,925 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:50:27,426 - INFO - 第 2 次刷新完成
2025-08-03 23:50:28,928 - INFO - 正在执行第 3 次刷新操作...
2025-08-03 23:50:28,929 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:50:28,929 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:50:28,937 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:50:31,707 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:50:32,733 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:50:32,734 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:50:35,238 - INFO - 第 3 次刷新完成
2025-08-03 23:50:35,238 - INFO - ✅ 自动刷新操作全部完成
2025-08-03 23:50:35,241 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-03 23:50:35,242 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-03 23:50:35,242 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-03 23:50:35,242 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-03 23:50:36,252 - INFO - 检测到Cookie文件已生成。
2025-08-03 23:50:36,253 - INFO - 从文件中解析到有效Cookie数据。
2025-08-03 23:50:36,253 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-03 23:50:36,253 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-03 23:50:36,254 - INFO - 正在停止Cookie抓取器 (PID: 23472)...
2025-08-03 23:50:36,265 - INFO - Cookie抓取器已成功终止。
2025-08-03 23:50:39,275 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-03 23:50:39,275 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-03 23:50:41,351 - WARNING - ⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。
2025-08-03 23:50:41,351 - INFO - ================================================================================
2025-08-03 23:50:41,351 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-03 23:50:41,352 - INFO - ================================================================================
2025-08-03 23:51:20,678 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-03 23:51:20,678 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-03 23:51:20,687 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-03 23:51:20,689 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-03 23:51:20,689 - INFO - ================================================================================
2025-08-03 23:51:20,689 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-03 23:51:20,690 - INFO - ================================================================================
2025-08-03 23:51:20,690 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-03 23:51:20,701 - INFO - 正在后台启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-03 23:51:25,750 - INFO - Cookie抓取器已在后台运行 (PID: 30860)。
2025-08-03 23:51:25,750 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-03 23:51:25,751 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-03 23:51:25,751 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-03 23:51:25,752 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-03 23:51:26,266 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-03 23:51:26,266 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-03 23:51:26,266 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-03 23:51:26,267 - INFO - 准备将链接发送到文件传输助手...
2025-08-03 23:51:26,267 - INFO - 正在查找微信主窗口...
2025-08-03 23:51:26,506 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-03 23:51:26,507 - INFO - 正在激活微信窗口...
2025-08-03 23:51:29,050 - INFO - 微信窗口已激活。
2025-08-03 23:51:29,050 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-03 23:51:35,992 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-03 23:51:35,993 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-03 23:51:39,406 - INFO - 正在查找聊天输入框...
2025-08-03 23:51:41,420 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-03 23:51:41,426 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-03 23:51:41,427 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-03 23:51:43,033 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-03 23:51:45,403 - INFO - 链接已粘贴，正在发送...
2025-08-03 23:51:45,957 - INFO - 找到发送按钮，点击发送...
2025-08-03 23:51:46,893 - INFO - 链接已发送。
2025-08-03 23:51:49,909 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-03 23:51:52,139 - INFO - 已定位到最新的消息项，准备点击。
2025-08-03 23:51:52,943 - INFO - ✅ 成功点击最新链接。
2025-08-03 23:51:52,943 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-03 23:51:55,953 - INFO - 正在执行第 1 次刷新操作...
2025-08-03 23:51:55,954 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:51:55,954 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:51:55,962 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:51:58,719 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:51:59,745 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:51:59,745 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:52:02,258 - INFO - 第 1 次刷新完成
2025-08-03 23:52:03,768 - INFO - 正在执行第 2 次刷新操作...
2025-08-03 23:52:03,768 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:52:03,768 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:52:03,776 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:52:06,557 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:52:07,588 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:52:07,589 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:52:10,100 - INFO - 第 2 次刷新完成
2025-08-03 23:52:11,612 - INFO - 正在执行第 3 次刷新操作...
2025-08-03 23:52:11,612 - INFO - 正在查找微信浏览器窗口...
2025-08-03 23:52:11,612 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-03 23:52:11,619 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-03 23:52:14,379 - INFO - 已成功激活焦点浏览器窗口
2025-08-03 23:52:15,392 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-03 23:52:15,392 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-03 23:52:17,906 - INFO - 第 3 次刷新完成
2025-08-03 23:52:17,906 - INFO - ✅ 自动刷新操作全部完成
2025-08-03 23:52:17,908 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-03 23:52:17,909 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-03 23:52:17,909 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-03 23:52:17,909 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-03 23:52:18,915 - INFO - 检测到Cookie文件已生成。
2025-08-03 23:52:18,916 - INFO - 从文件中解析到有效Cookie数据。
2025-08-03 23:52:18,917 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-03 23:52:18,917 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-03 23:52:18,917 - INFO - 正在停止Cookie抓取器 (PID: 30860)...
2025-08-03 23:52:18,931 - INFO - Cookie抓取器已成功终止。
2025-08-03 23:52:21,947 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-03 23:52:21,947 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-03 23:52:23,986 - WARNING - ⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。
2025-08-03 23:52:23,986 - INFO - ================================================================================
2025-08-03 23:52:23,986 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-03 23:52:23,987 - INFO - ================================================================================
2025-08-04 00:07:01,413 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:07:01,414 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:07:01,423 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:07:01,424 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:07:01,424 - INFO - ================================================================================
2025-08-04 00:07:01,424 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:07:01,425 - INFO - ================================================================================
2025-08-04 00:07:01,425 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:07:01,435 - INFO - 准备启动Cookie抓取器，首先清理网络状态...
2025-08-04 00:07:01,435 - INFO - 重置网络状态...
2025-08-04 00:07:13,930 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:07:13,930 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:07:13,938 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:07:13,938 - INFO - ================================================================================
2025-08-04 00:07:13,939 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:07:13,940 - INFO - ================================================================================
2025-08-04 00:07:13,940 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:07:13,940 - INFO - 准备启动Cookie抓取器，首先清理网络状态...
2025-08-04 00:07:13,941 - INFO - 重置网络状态...
2025-08-04 00:07:30,175 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:07:30,175 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:07:30,182 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:07:30,183 - INFO - ================================================================================
2025-08-04 00:07:30,183 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:07:30,184 - INFO - ================================================================================
2025-08-04 00:07:30,184 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:07:30,184 - INFO - 准备启动Cookie抓取器，首先清理网络状态...
2025-08-04 00:07:30,196 - INFO - 重置网络状态...
2025-08-04 00:19:06,494 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:19:06,494 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:19:06,503 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:19:06,503 - INFO - ================================================================================
2025-08-04 00:19:06,504 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:19:06,504 - INFO - ================================================================================
2025-08-04 00:19:06,505 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:19:06,505 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 00:19:06,507 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 00:19:06,507 - INFO - === 开始重置网络状态 ===
2025-08-04 00:19:06,507 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 00:30:50,020 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:30:50,021 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:30:50,029 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:30:50,030 - INFO - ================================================================================
2025-08-04 00:30:50,030 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:30:50,030 - INFO - ================================================================================
2025-08-04 00:30:50,031 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:30:50,031 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 00:30:50,040 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 00:30:50,040 - INFO - === 开始重置网络状态 ===
2025-08-04 00:30:50,040 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 00:30:50,247 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 00:30:50,247 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 00:30:50,247 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 00:30:50,247 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 00:30:50,247 - INFO - 🔗 正在验证网络连接...
2025-08-04 00:30:52,303 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000273CFBB7B80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 00:30:52,304 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 00:30:52,304 - INFO - 🔄 简要重试检查 1/2
2025-08-04 00:30:54,355 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000273CFBB7C70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 00:30:54,356 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 00:30:54,356 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 00:30:54,356 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 00:30:54,357 - INFO - 已备份原始代理设置: {'enable': True, 'server': '127.0.0.1:8080'}
2025-08-04 00:30:54,358 - INFO - 步骤3: 正在启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 00:30:55,573 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1 binary
Python:    3.13.3
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-10-10.0.19045-SP0
2025-08-04 00:30:55,615 - INFO - 🔄 Cookie抓取器进程已启动，PID: 22924
2025-08-04 00:30:55,615 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 00:30:58,619 - INFO - 等待代理服务启动...
2025-08-04 00:30:59,257 - INFO - 代理服务已启动并正常工作
2025-08-04 00:30:59,257 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 22924)
2025-08-04 00:30:59,257 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 00:30:59,258 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 00:30:59,258 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:30:59,259 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 00:30:59,863 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 00:30:59,863 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 00:30:59,864 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 00:30:59,864 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 00:30:59,864 - INFO - 正在查找微信主窗口...
2025-08-04 00:31:00,136 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 00:31:00,136 - INFO - 正在激活微信窗口...
2025-08-04 00:31:02,674 - INFO - 微信窗口已激活。
2025-08-04 00:31:02,674 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 00:31:09,404 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 00:31:09,405 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 00:31:12,799 - INFO - 正在查找聊天输入框...
2025-08-04 00:31:14,806 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 00:31:14,813 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 00:31:14,814 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-04 00:31:16,455 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 00:31:18,856 - INFO - 链接已粘贴，正在发送...
2025-08-04 00:31:19,448 - INFO - 找到发送按钮，点击发送...
2025-08-04 00:31:20,354 - INFO - 链接已发送。
2025-08-04 00:31:23,365 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 00:31:25,598 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 00:31:26,411 - INFO - ✅ 成功点击最新链接。
2025-08-04 00:31:26,411 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 00:31:29,421 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 00:31:29,422 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:31:29,422 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:31:29,431 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:31:32,205 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:31:33,234 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:31:33,234 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:31:35,738 - INFO - 第 1 次刷新完成
2025-08-04 00:31:37,249 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 00:31:37,249 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:31:37,250 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:31:37,257 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:31:40,028 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:31:41,044 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:31:41,046 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:31:43,555 - INFO - 第 2 次刷新完成
2025-08-04 00:31:45,066 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 00:31:45,066 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:31:45,066 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:31:45,076 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:31:47,844 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:31:48,861 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:31:48,862 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:31:51,365 - INFO - 第 3 次刷新完成
2025-08-04 00:31:51,365 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 00:31:51,368 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 00:31:51,369 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 00:31:51,369 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 00:31:51,370 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 00:31:52,377 - INFO - 检测到Cookie文件已生成。
2025-08-04 00:31:52,378 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 00:31:52,379 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 00:31:52,379 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-04 00:31:52,380 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 00:31:52,380 - INFO - 正在停止Cookie抓取器 (PID: 22924)...
2025-08-04 00:31:52,393 - INFO - Cookie抓取器已成功终止。
2025-08-04 00:31:52,394 - INFO - 正在验证并清理代理设置...
2025-08-04 00:31:52,394 - INFO - === 开始重置网络状态 ===
2025-08-04 00:31:52,395 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 00:31:52,623 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 00:31:52,624 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 00:31:52,624 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 00:31:52,624 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 00:31:52,625 - INFO - 🔗 正在验证网络连接...
2025-08-04 00:31:54,652 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000273D0314040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 00:31:54,653 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 00:31:54,653 - INFO - 🔄 简要重试检查 1/2
2025-08-04 00:31:56,692 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000273D0314A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 00:31:56,693 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 00:31:56,693 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 00:31:56,694 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 00:31:58,745 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000273D03148B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 00:31:58,745 - WARNING - ⚠️ 网络连接验证失败，可能需要手动检查
2025-08-04 00:32:01,759 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 00:32:01,760 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-04 00:32:03,789 - WARNING - ⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。
2025-08-04 00:32:03,789 - INFO - ================================================================================
2025-08-04 00:32:03,790 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-04 00:32:03,790 - INFO - ================================================================================
2025-08-04 00:32:27,349 - INFO - Imported existing <module 'comtypes.gen' from 'E:\\mynj\\mynj_env\\lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 00:32:27,350 - INFO - Using writeable comtypes cache directory: 'E:\mynj\mynj_env\lib\site-packages\comtypes\gen'
2025-08-04 00:32:27,359 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:32:27,360 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:32:27,361 - INFO - ================================================================================
2025-08-04 00:32:27,361 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:32:27,361 - INFO - ================================================================================
2025-08-04 00:32:27,362 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:32:27,372 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 00:32:27,372 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 00:32:27,372 - INFO - === 开始重置网络状态 ===
2025-08-04 00:32:27,372 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 00:32:27,572 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 00:32:27,573 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 00:32:27,573 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 00:32:27,573 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 00:32:27,574 - INFO - 🔗 正在验证网络连接...
2025-08-04 00:32:28,780 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 00:32:28,781 - INFO - ✅ 网络状态重置验证完成
2025-08-04 00:32:28,781 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 00:32:28,782 - INFO - 已备份原始代理设置: {'enable': False, 'server': '127.0.0.1:8080'}
2025-08-04 00:32:28,782 - INFO - 步骤3: 正在启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 00:32:29,964 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1 binary
Python:    3.13.3
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-10-10.0.19045-SP0
2025-08-04 00:32:30,007 - INFO - 🔄 Cookie抓取器进程已启动，PID: 10996
2025-08-04 00:32:30,007 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 00:32:33,012 - INFO - 等待代理服务启动...
2025-08-04 00:32:33,595 - INFO - 代理服务已启动并正常工作
2025-08-04 00:32:33,595 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 10996)
2025-08-04 00:32:33,596 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 00:32:33,596 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 00:32:33,597 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:32:33,598 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 00:32:34,251 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 00:32:34,251 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 00:32:34,252 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 00:32:34,252 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 00:32:34,252 - INFO - 正在查找微信主窗口...
2025-08-04 00:32:35,701 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 00:32:35,701 - INFO - 正在激活微信窗口...
2025-08-04 00:32:38,254 - INFO - 微信窗口已激活。
2025-08-04 00:32:38,254 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 00:32:45,166 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 00:32:45,166 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 00:32:48,588 - INFO - 正在查找聊天输入框...
2025-08-04 00:32:50,600 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 00:32:50,607 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 00:32:50,607 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-04 00:32:52,229 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 00:32:54,592 - INFO - 链接已粘贴，正在发送...
2025-08-04 00:32:55,233 - INFO - 找到发送按钮，点击发送...
2025-08-04 00:32:56,150 - INFO - 链接已发送。
2025-08-04 00:32:59,157 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 00:33:01,393 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 00:33:02,225 - INFO - ✅ 成功点击最新链接。
2025-08-04 00:33:02,225 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 00:33:05,225 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 00:33:05,225 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:05,226 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:05,233 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:08,009 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:09,036 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:09,037 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:11,549 - INFO - 第 1 次刷新完成
2025-08-04 00:33:13,060 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 00:33:13,060 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:13,061 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:13,069 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:15,824 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:16,854 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:16,855 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:19,365 - INFO - 第 2 次刷新完成
2025-08-04 00:33:20,876 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 00:33:20,876 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:20,877 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:20,884 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:23,663 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:24,692 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:24,693 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:27,208 - INFO - 第 3 次刷新完成
2025-08-04 00:33:27,209 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 00:33:27,213 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 00:33:27,213 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 00:33:27,214 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 00:33:27,214 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 09:38:07,351 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 09:38:07,352 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 09:38:07,367 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 09:38:07,367 - INFO - ================================================================================
2025-08-04 09:38:07,367 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 09:38:07,367 - INFO - ================================================================================
2025-08-04 09:38:07,367 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 09:38:07,367 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 09:38:07,368 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 09:38:07,368 - INFO - === 开始重置网络状态 ===
2025-08-04 09:38:07,368 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 09:38:07,457 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 09:38:07,458 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 09:38:07,458 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 09:38:07,458 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 09:38:07,458 - INFO - 🔗 正在验证网络连接...
2025-08-04 09:38:08,397 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 09:38:08,398 - INFO - ✅ 网络状态重置验证完成
2025-08-04 09:38:08,398 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 09:38:08,399 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 09:38:08,400 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 09:38:11,570 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 09:38:11,571 - INFO - 🔄 Cookie抓取器进程已启动，PID: 5040
2025-08-04 09:38:11,571 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 09:38:14,572 - INFO - 等待代理服务启动...
2025-08-04 09:38:14,997 - INFO - 代理服务已启动并正常工作
2025-08-04 09:38:14,997 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 5040)
2025-08-04 09:38:14,998 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 09:38:14,998 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 09:38:14,998 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 09:38:14,999 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 09:38:15,498 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 09:38:15,499 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 09:38:15,499 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 09:38:15,499 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 09:38:15,500 - INFO - 正在查找微信主窗口...
2025-08-04 09:38:15,862 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 09:38:15,862 - INFO - 正在激活微信窗口...
2025-08-04 09:38:18,380 - INFO - 微信窗口已激活。
2025-08-04 09:38:18,381 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 09:38:25,224 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 09:38:25,224 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 09:38:28,552 - INFO - 正在查找聊天输入框...
2025-08-04 09:38:30,553 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 09:38:30,564 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 09:38:30,564 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 09:38:32,148 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 09:38:34,436 - INFO - 链接已粘贴，正在发送...
2025-08-04 09:38:34,537 - INFO - 找到发送按钮，点击发送...
2025-08-04 09:38:35,340 - INFO - 链接已发送。
2025-08-04 09:38:38,341 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 09:38:40,427 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 09:38:41,272 - INFO - ✅ 成功点击最新链接。
2025-08-04 09:38:41,273 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 09:38:44,273 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 09:38:44,274 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:44,274 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:44,281 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:38:46,988 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:38:47,991 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:38:47,992 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:38:50,493 - INFO - 第 1 次刷新完成
2025-08-04 09:38:51,994 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 09:38:51,995 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:51,996 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:52,006 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:38:54,712 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:38:55,714 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:38:55,715 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:38:58,215 - INFO - 第 2 次刷新完成
2025-08-04 09:38:59,716 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 09:38:59,717 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:59,718 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:59,729 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:39:02,434 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:39:03,438 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:39:03,438 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:39:05,939 - INFO - 第 3 次刷新完成
2025-08-04 09:39:05,940 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 09:39:05,941 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 09:39:05,941 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 09:39:05,942 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 09:39:05,942 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 09:41:05,985 - ERROR - 等待Cookie超时！
2025-08-04 09:41:05,985 - ERROR - ❌ 等待 Cookie 超时，流程中止。
2025-08-04 09:41:05,986 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 09:41:05,986 - INFO - 正在停止Cookie抓取器 (PID: 5040)...
2025-08-04 09:41:05,986 - INFO - Cookie抓取器已成功终止。
2025-08-04 09:41:05,987 - INFO - 正在验证并清理代理设置...
2025-08-04 09:41:05,987 - INFO - === 开始重置网络状态 ===
2025-08-04 09:41:05,987 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 09:41:06,152 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 09:41:06,152 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 09:41:06,152 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 09:41:06,152 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 09:41:06,152 - INFO - 🔗 正在验证网络连接...
2025-08-04 09:41:08,189 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFC7D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:08,189 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 09:41:08,189 - INFO - 🔄 简要重试检查 1/2
2025-08-04 09:41:10,246 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFCCD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:10,246 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 09:41:10,246 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 09:41:10,246 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 09:41:12,306 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFCF50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:12,306 - WARNING - ⚠️ 网络连接验证失败，可能需要手动检查
2025-08-04 10:53:47,813 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 10:53:47,813 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 10:53:47,818 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 10:53:47,818 - INFO - ================================================================================
2025-08-04 10:53:47,818 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 10:53:47,818 - INFO - ================================================================================
2025-08-04 10:53:47,819 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 10:53:47,819 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 10:53:47,819 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 10:53:47,819 - INFO - === 开始重置网络状态 ===
2025-08-04 10:53:47,819 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:53:47,908 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:53:47,908 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:53:47,908 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 10:53:47,909 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 10:53:47,909 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:53:49,962 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:53:49,962 - INFO - 验证中: 代理状态=True, 网络状态=True
2025-08-04 10:53:49,963 - INFO - 🔄 简要重试检查 1/2
2025-08-04 10:53:51,434 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:53:51,435 - INFO - 验证中: 代理状态=True, 网络状态=True
2025-08-04 10:53:51,435 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 10:53:51,436 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 10:53:51,436 - INFO - 已备份原始代理设置: {'enable': True, 'server': '127.0.0.1:7897'}
2025-08-04 10:53:51,437 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 10:53:53,580 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 10:53:53,581 - INFO - 🔄 Cookie抓取器进程已启动，PID: 9628
2025-08-04 10:53:53,581 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 10:53:56,582 - INFO - 等待代理服务启动...
2025-08-04 10:53:58,074 - INFO - 代理服务已启动并正常工作
2025-08-04 10:53:58,074 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 9628)
2025-08-04 10:53:58,074 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 10:53:58,074 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 10:53:58,075 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 10:53:58,075 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 10:53:58,415 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 10:53:58,416 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 10:53:58,416 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 10:53:58,417 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 10:53:58,417 - INFO - 正在查找微信主窗口...
2025-08-04 10:53:58,773 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 10:53:58,774 - INFO - 正在激活微信窗口...
2025-08-04 10:54:01,286 - INFO - 微信窗口已激活。
2025-08-04 10:54:01,287 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 10:54:08,030 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 10:54:08,031 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 10:54:11,360 - INFO - 正在查找聊天输入框...
2025-08-04 10:54:13,361 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 10:54:13,371 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 10:54:13,371 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 10:54:14,964 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 10:54:17,254 - INFO - 链接已粘贴，正在发送...
2025-08-04 10:54:17,355 - INFO - 找到发送按钮，点击发送...
2025-08-04 10:54:18,161 - INFO - 链接已发送。
2025-08-04 10:54:21,162 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 10:54:23,247 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 10:54:24,009 - INFO - ✅ 成功点击最新链接。
2025-08-04 10:54:24,010 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 10:54:27,010 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 10:54:27,011 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:54:27,012 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:54:27,023 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:54:29,730 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:54:30,733 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:54:30,734 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:54:33,234 - INFO - 第 1 次刷新完成
2025-08-04 10:54:34,736 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 10:54:34,737 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:54:34,738 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:54:34,754 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 10:54:37,460 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:54:38,462 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:54:38,463 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:55:08,336 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 10:55:08,337 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 10:55:08,341 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 10:55:08,342 - INFO - ================================================================================
2025-08-04 10:55:08,342 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 10:55:08,342 - INFO - ================================================================================
2025-08-04 10:55:08,342 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 10:55:08,342 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 10:55:08,343 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 10:55:08,343 - INFO - === 开始重置网络状态 ===
2025-08-04 10:55:08,343 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:55:08,413 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:55:08,413 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:55:08,413 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 10:55:08,413 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 10:55:08,413 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:55:09,197 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:55:09,198 - INFO - ✅ 网络状态重置验证完成
2025-08-04 10:55:09,198 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 10:55:09,199 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 10:55:09,199 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 10:55:09,665 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 10:55:09,665 - INFO - 🔄 Cookie抓取器进程已启动，PID: 25312
2025-08-04 10:55:09,666 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 10:55:12,666 - INFO - 等待代理服务启动...
2025-08-04 10:55:13,104 - INFO - 代理服务已启动并正常工作
2025-08-04 10:55:13,105 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 25312)
2025-08-04 10:55:13,105 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 10:55:13,106 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 10:55:13,107 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 10:55:13,107 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 10:55:13,179 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 10:55:13,180 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 10:55:13,180 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 10:55:13,180 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 10:55:13,180 - INFO - 正在查找微信主窗口...
2025-08-04 10:55:13,343 - ERROR - ❌ UI 自动化过程中发生错误: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
2025-08-04 10:55:13,343 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 10:55:13,343 - INFO - 正在停止Cookie抓取器 (PID: 25312)...
2025-08-04 10:55:13,344 - INFO - Cookie抓取器已成功终止。
2025-08-04 10:55:13,344 - INFO - 正在验证并清理代理设置...
2025-08-04 10:55:13,344 - INFO - === 开始重置网络状态 ===
2025-08-04 10:55:13,344 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:55:13,442 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:55:13,442 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:55:13,442 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 10:55:13,442 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 10:55:13,442 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:55:15,471 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002A1C47087D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 10:55:15,472 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 10:55:15,473 - INFO - 🔄 简要重试检查 1/2
2025-08-04 10:55:17,513 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002A1C4708CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 10:55:17,514 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 10:55:17,514 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 10:55:17,515 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 10:55:19,551 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002A1C4708F50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 10:55:19,552 - WARNING - ⚠️ 网络连接验证失败，可能需要手动检查
2025-08-04 10:57:53,501 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 10:57:53,501 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 10:57:53,507 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 10:57:53,508 - INFO - ================================================================================
2025-08-04 10:57:53,508 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 10:57:53,508 - INFO - ================================================================================
2025-08-04 10:57:53,508 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 10:57:53,508 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 10:57:53,508 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 10:57:53,508 - INFO - === 开始重置网络状态 ===
2025-08-04 10:57:53,508 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:57:53,585 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:57:53,585 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:57:53,585 - INFO - 系统代理已成功关闭
2025-08-04 10:57:53,585 - INFO - ✅ 代理关闭操作
2025-08-04 10:57:53,586 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:57:54,529 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:57:54,530 - INFO - ✅ 网络状态重置验证完成
2025-08-04 10:57:54,531 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 10:57:54,532 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 10:57:54,533 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 10:57:54,992 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 10:57:54,993 - INFO - 🔄 Cookie抓取器进程已启动，PID: 14148
2025-08-04 10:57:54,993 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 10:57:57,993 - INFO - 等待代理服务启动...
2025-08-04 10:57:58,435 - INFO - 代理服务已启动并正常工作
2025-08-04 10:57:58,436 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 14148)
2025-08-04 10:57:58,436 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 10:57:58,437 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 10:57:58,437 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 10:57:58,438 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 10:57:58,511 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 10:57:58,511 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 10:57:58,512 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 10:57:58,512 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 10:57:58,512 - INFO - 正在查找微信主窗口...
2025-08-04 10:57:59,704 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 10:57:59,704 - INFO - 正在激活微信窗口...
2025-08-04 10:58:02,234 - INFO - 微信窗口已激活。
2025-08-04 10:58:02,234 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 10:58:09,130 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 10:58:09,130 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 10:58:12,462 - INFO - 正在查找聊天输入框...
2025-08-04 10:58:14,463 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 10:58:14,475 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 10:58:14,475 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 10:58:16,055 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 10:58:18,342 - INFO - 链接已粘贴，正在发送...
2025-08-04 10:58:18,442 - INFO - 找到发送按钮，点击发送...
2025-08-04 10:58:19,241 - INFO - 链接已发送。
2025-08-04 10:58:22,242 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 10:58:24,328 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 10:58:25,060 - INFO - ✅ 成功点击最新链接。
2025-08-04 10:58:25,060 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 10:58:28,061 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 10:58:28,061 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:58:28,062 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:58:28,071 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:58:30,778 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:58:31,780 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:58:31,781 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:58:34,282 - INFO - 第 1 次刷新完成
2025-08-04 10:58:35,783 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 10:58:35,783 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:58:35,784 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:58:35,793 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:58:38,498 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:58:39,501 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:58:39,501 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:58:42,002 - INFO - 第 2 次刷新完成
2025-08-04 10:58:43,503 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 10:58:43,504 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:58:43,505 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:58:43,513 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:58:59,896 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 10:58:59,896 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 10:58:59,901 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 10:58:59,901 - INFO - ================================================================================
2025-08-04 10:58:59,901 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 10:58:59,901 - INFO - ================================================================================
2025-08-04 10:58:59,901 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 10:58:59,901 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 10:58:59,903 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 10:58:59,903 - INFO - === 开始重置网络状态 ===
2025-08-04 10:58:59,903 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:58:59,982 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:58:59,983 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:58:59,983 - INFO - 系统代理已成功关闭
2025-08-04 10:58:59,983 - INFO - ✅ 代理关闭操作
2025-08-04 10:58:59,983 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:59:00,858 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:59:00,859 - INFO - ✅ 网络状态重置验证完成
2025-08-04 10:59:00,860 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 10:59:00,861 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 10:59:00,862 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 10:59:01,330 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 10:59:01,331 - INFO - 🔄 Cookie抓取器进程已启动，PID: 19868
2025-08-04 10:59:01,331 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 10:59:04,331 - INFO - 等待代理服务启动...
2025-08-04 10:59:04,749 - INFO - 代理服务已启动并正常工作
2025-08-04 10:59:04,749 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 19868)
2025-08-04 10:59:04,750 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 10:59:04,750 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 10:59:04,750 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 10:59:04,751 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 10:59:04,826 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 10:59:04,826 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 10:59:04,826 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 10:59:04,826 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 10:59:04,826 - INFO - 正在查找微信主窗口...
2025-08-04 10:59:04,923 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 10:59:04,923 - INFO - 正在激活微信窗口...
2025-08-04 10:59:07,436 - INFO - 微信窗口已激活。
2025-08-04 10:59:07,437 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 10:59:14,192 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 10:59:14,193 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 10:59:17,527 - INFO - 正在查找聊天输入框...
2025-08-04 10:59:19,528 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 10:59:19,538 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 10:59:19,538 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 10:59:21,140 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 10:59:23,427 - INFO - 链接已粘贴，正在发送...
2025-08-04 10:59:23,534 - INFO - 找到发送按钮，点击发送...
2025-08-04 10:59:24,360 - INFO - 链接已发送。
2025-08-04 10:59:27,361 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 10:59:29,442 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 10:59:30,176 - INFO - ✅ 成功点击最新链接。
2025-08-04 10:59:30,176 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 10:59:33,177 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 10:59:33,177 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:33,178 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:33,186 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:59:35,907 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:59:36,908 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:36,909 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:39,410 - INFO - 第 1 次刷新完成
2025-08-04 10:59:40,911 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 10:59:40,912 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:40,912 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:40,915 - INFO - 尝试传统窗口查找方法...
2025-08-04 10:59:43,833 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 10:59:45,650 - INFO - 已成功激活浏览器窗口
2025-08-04 10:59:46,652 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:46,653 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:49,153 - INFO - 第 2 次刷新完成
2025-08-04 10:59:50,655 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 10:59:50,656 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:50,656 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:50,659 - INFO - 尝试传统窗口查找方法...
2025-08-04 10:59:53,501 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 10:59:55,317 - INFO - 已成功激活浏览器窗口
2025-08-04 10:59:56,319 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:56,320 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:58,821 - INFO - 第 3 次刷新完成
2025-08-04 10:59:58,822 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 10:59:58,822 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 10:59:58,823 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 10:59:58,823 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 10:59:58,824 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 10:59:59,824 - INFO - 检测到Cookie文件已生成。
2025-08-04 10:59:59,839 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 10:59:59,840 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 10:59:59,840 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-04 10:59:59,840 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 10:59:59,841 - INFO - 正在停止Cookie抓取器 (PID: 19868)...
2025-08-04 10:59:59,842 - INFO - Cookie抓取器已成功终止。
2025-08-04 10:59:59,843 - INFO - 正在验证并清理代理设置...
2025-08-04 10:59:59,843 - INFO - === 开始重置网络状态 ===
2025-08-04 10:59:59,843 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:59:59,924 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:59:59,924 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:59:59,924 - INFO - 系统代理已成功关闭
2025-08-04 10:59:59,924 - INFO - ✅ 代理关闭操作
2025-08-04 10:59:59,924 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:00:01,850 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:00:01,850 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:00:01,850 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 11:00:03,665 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:00:03,666 - INFO - ✅ 网络连接验证正常
2025-08-04 11:00:06,666 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 11:00:06,667 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-04 11:34:05,402 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 11:34:05,408 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 11:34:05,420 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 11:34:05,420 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 11:34:05,420 - INFO - ================================================================================
2025-08-04 11:34:05,420 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 11:34:05,420 - INFO - ================================================================================
2025-08-04 11:34:05,421 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 11:34:05,421 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 11:34:05,421 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 11:34:05,421 - INFO - === 开始重置网络状态 ===
2025-08-04 11:34:05,421 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 11:34:05,507 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 11:34:05,507 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 11:34:05,507 - INFO - 系统代理已成功关闭
2025-08-04 11:34:05,507 - INFO - ✅ 代理关闭操作
2025-08-04 11:34:05,507 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:34:07,506 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:34:07,507 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:34:07,507 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 11:34:07,508 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 11:34:07,509 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 11:34:08,043 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 11:34:08,044 - INFO - 🔄 Cookie抓取器进程已启动，PID: 17888
2025-08-04 11:34:08,044 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 11:34:11,045 - INFO - 等待代理服务启动...
2025-08-04 11:34:11,466 - INFO - 代理服务已启动并正常工作
2025-08-04 11:34:11,467 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 17888)
2025-08-04 11:34:11,467 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 11:34:11,468 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 11:34:11,469 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 11:34:11,469 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 11:34:11,794 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 11:34:11,794 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 11:34:11,795 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 11:34:11,795 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 11:34:11,795 - INFO - 正在查找微信主窗口...
2025-08-04 11:34:12,637 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 11:34:12,637 - INFO - 正在激活微信窗口...
2025-08-04 11:34:15,151 - INFO - 微信窗口已激活。
2025-08-04 11:34:15,152 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 11:34:21,863 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 11:34:21,864 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 11:34:25,194 - INFO - 正在查找聊天输入框...
2025-08-04 11:34:27,195 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 11:34:27,205 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 11:34:27,206 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 11:34:28,809 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 11:34:31,096 - INFO - 链接已粘贴，正在发送...
2025-08-04 11:34:31,212 - INFO - 找到发送按钮，点击发送...
2025-08-04 11:34:32,028 - INFO - 链接已发送。
2025-08-04 11:34:35,028 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 11:34:37,110 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 11:34:37,844 - INFO - ✅ 成功点击最新链接。
2025-08-04 11:34:37,844 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 11:34:40,845 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 11:34:40,845 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:34:40,846 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:34:40,857 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:34:43,565 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:34:44,066 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:34:44,067 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:34:44,077 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:34:46,784 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:34:53,064 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:34:53,065 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:34:55,566 - INFO - 第 1 次刷新完成
2025-08-04 11:34:57,067 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 11:34:57,067 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:34:57,068 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:34:57,077 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:34:59,781 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:35:00,282 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:35:00,282 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:35:00,292 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:35:02,998 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:35:09,330 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:35:09,331 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:35:11,832 - INFO - 第 2 次刷新完成
2025-08-04 11:35:13,333 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 11:35:13,334 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:35:13,334 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:35:13,340 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:35:16,046 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:35:16,547 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:35:16,547 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:35:16,551 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:35:19,255 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:35:25,531 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:35:25,531 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:35:28,033 - INFO - 第 3 次刷新完成
2025-08-04 11:35:28,034 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 11:35:28,034 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 11:35:28,035 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 11:35:28,035 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 11:35:28,035 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 11:37:28,081 - ERROR - 等待Cookie超时！
2025-08-04 11:37:28,081 - ERROR - ❌ 等待 Cookie 超时，流程中止。
2025-08-04 11:37:28,082 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 11:37:28,082 - INFO - 正在停止Cookie抓取器 (PID: 17888)...
2025-08-04 11:37:28,085 - INFO - Cookie抓取器已成功终止。
2025-08-04 11:37:28,085 - INFO - 正在验证并清理代理设置...
2025-08-04 11:37:28,085 - INFO - === 开始重置网络状态 ===
2025-08-04 11:37:28,085 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 11:37:28,242 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 11:37:28,242 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 11:37:28,242 - INFO - 系统代理已成功关闭
2025-08-04 11:37:28,242 - INFO - ✅ 代理关闭操作
2025-08-04 11:37:28,243 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:37:30,275 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:37:30,276 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:37:30,276 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 11:37:31,695 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:37:31,696 - INFO - ✅ 网络连接验证正常
2025-08-04 11:39:27,962 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 11:39:27,963 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 11:39:27,967 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 11:39:27,967 - INFO - ================================================================================
2025-08-04 11:39:27,967 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 11:39:27,967 - INFO - ================================================================================
2025-08-04 11:39:27,967 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 11:39:27,967 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 11:39:27,969 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 11:39:27,969 - INFO - === 开始重置网络状态 ===
2025-08-04 11:39:27,969 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 11:39:28,072 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 11:39:28,072 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 11:39:28,073 - INFO - 系统代理已成功关闭
2025-08-04 11:39:28,073 - INFO - ✅ 代理关闭操作
2025-08-04 11:39:28,073 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:39:28,954 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:39:28,955 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:39:28,956 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 11:39:28,957 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 11:39:28,958 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 11:39:29,454 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 11:39:29,455 - INFO - 🔄 Cookie抓取器进程已启动，PID: 6988
2025-08-04 11:39:29,455 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 11:39:32,456 - INFO - 等待代理服务启动...
2025-08-04 11:39:32,880 - INFO - 代理服务已启动并正常工作
2025-08-04 11:39:32,880 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 6988)
2025-08-04 11:39:32,881 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 11:39:32,881 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 11:39:32,881 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 11:39:32,881 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 11:39:32,958 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 11:39:32,958 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 11:39:32,958 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 11:39:32,958 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 11:39:32,959 - INFO - 正在查找微信主窗口...
2025-08-04 11:39:33,110 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 11:39:33,110 - INFO - 正在激活微信窗口...
2025-08-04 11:39:35,624 - INFO - 微信窗口已激活。
2025-08-04 11:39:35,625 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 11:39:42,297 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 11:39:42,298 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 11:39:45,627 - INFO - 正在查找聊天输入框...
2025-08-04 11:39:47,628 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 11:39:47,643 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 11:39:47,644 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 11:39:49,242 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 11:39:51,530 - INFO - 链接已粘贴，正在发送...
2025-08-04 11:39:51,641 - INFO - 找到发送按钮，点击发送...
2025-08-04 11:39:52,444 - INFO - 链接已发送。
2025-08-04 11:39:55,446 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 11:39:57,529 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 11:39:58,260 - INFO - ✅ 成功点击最新链接。
2025-08-04 11:39:58,261 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 11:40:01,261 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 11:40:01,262 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:01,262 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:01,266 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:03,971 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:04,472 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:04,473 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:04,484 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:07,194 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:13,494 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:40:13,495 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:40:15,996 - INFO - 第 1 次刷新完成
2025-08-04 11:40:17,497 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 11:40:17,497 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:17,497 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:17,501 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:20,207 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:20,708 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:20,709 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:20,718 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:23,425 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:29,704 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:40:29,705 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:40:32,206 - INFO - 第 2 次刷新完成
2025-08-04 11:40:33,708 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 11:40:33,708 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:33,708 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:33,711 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:36,416 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:36,917 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:40:36,918 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:40:36,930 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:40:39,635 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:40:45,923 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:40:45,924 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:40:48,425 - INFO - 第 3 次刷新完成
2025-08-04 11:40:48,426 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 11:40:48,427 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 11:40:48,427 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 11:40:48,428 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 11:40:48,428 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 11:47:33,112 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 11:47:33,112 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 11:47:33,117 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 11:47:33,117 - INFO - ================================================================================
2025-08-04 11:47:33,117 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 11:47:33,117 - INFO - ================================================================================
2025-08-04 11:47:33,117 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 11:47:33,117 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 11:47:33,119 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 11:47:33,119 - INFO - === 开始重置网络状态 ===
2025-08-04 11:47:33,119 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 11:47:33,206 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 11:47:33,207 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 11:47:33,207 - INFO - 系统代理已成功关闭
2025-08-04 11:47:33,207 - INFO - ✅ 代理关闭操作
2025-08-04 11:47:33,207 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:47:35,039 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:47:35,039 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:47:35,040 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 11:47:35,040 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 11:47:35,040 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 11:47:35,581 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 11:47:35,582 - INFO - 🔄 Cookie抓取器进程已启动，PID: 25768
2025-08-04 11:47:35,582 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 11:47:38,583 - INFO - 等待代理服务启动...
2025-08-04 11:47:43,563 - INFO - 代理服务已启动并正常工作
2025-08-04 11:47:43,563 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 25768)
2025-08-04 11:47:43,564 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 11:47:43,564 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 11:47:43,565 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 11:47:43,566 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 11:47:43,640 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 11:47:43,640 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 11:47:43,640 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 11:47:43,641 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 11:47:43,641 - INFO - 正在查找微信主窗口...
2025-08-04 11:47:44,383 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 11:47:44,384 - INFO - 正在激活微信窗口...
2025-08-04 11:47:46,898 - INFO - 微信窗口已激活。
2025-08-04 11:47:46,899 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 11:47:53,831 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 11:47:53,832 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 11:47:57,161 - INFO - 正在查找聊天输入框...
2025-08-04 11:47:59,162 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 11:47:59,170 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 11:47:59,171 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 11:48:00,748 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 11:48:03,035 - INFO - 链接已粘贴，正在发送...
2025-08-04 11:48:03,156 - INFO - 找到发送按钮，点击发送...
2025-08-04 11:48:03,961 - INFO - 链接已发送。
2025-08-04 11:48:06,962 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 11:48:09,045 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 11:48:09,777 - INFO - ✅ 成功点击最新链接。
2025-08-04 11:48:09,778 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 11:48:12,778 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 11:48:12,779 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:12,779 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:12,786 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:15,493 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:15,994 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:15,995 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:16,000 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:18,706 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:24,950 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:48:24,951 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:48:27,452 - INFO - 第 1 次刷新完成
2025-08-04 11:48:28,953 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 11:48:28,953 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:28,953 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:28,958 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:31,663 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:32,164 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:32,165 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:32,176 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:34,882 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:41,145 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:48:41,146 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:48:43,646 - INFO - 第 2 次刷新完成
2025-08-04 11:48:45,147 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 11:48:45,147 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:45,148 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:45,154 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:47,861 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:48,362 - INFO - 正在查找微信浏览器窗口...
2025-08-04 11:48:48,362 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 11:48:48,370 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 11:48:51,076 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 11:48:57,373 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 11:48:57,374 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 11:48:59,875 - INFO - 第 3 次刷新完成
2025-08-04 11:48:59,876 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 11:48:59,876 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 11:48:59,877 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 11:48:59,877 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 11:48:59,877 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 11:59:23,581 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 11:59:23,582 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:05:47,557 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 12:05:47,557 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:05:47,562 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 12:05:47,562 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:05:47,562 - INFO - ================================================================================
2025-08-04 12:05:47,563 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 12:05:47,563 - INFO - ================================================================================
2025-08-04 12:05:47,563 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 12:05:47,564 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 12:05:47,564 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 12:05:47,564 - INFO - === 开始重置网络状态 ===
2025-08-04 12:05:47,564 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:05:47,652 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:05:47,652 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:05:47,652 - INFO - 系统代理已成功关闭
2025-08-04 12:05:47,653 - INFO - ✅ 代理关闭操作
2025-08-04 12:05:47,653 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:05:48,566 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:05:48,567 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:05:48,567 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 12:05:48,568 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 12:05:48,569 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 12:05:49,087 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 12:05:49,087 - INFO - 🔄 Cookie抓取器进程已启动，PID: 16204
2025-08-04 12:05:49,088 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 12:05:52,088 - INFO - 等待代理服务启动...
2025-08-04 12:05:54,088 - INFO - 代理服务已启动并正常工作
2025-08-04 12:05:54,089 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 16204)
2025-08-04 12:05:54,089 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 12:05:54,090 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 12:05:54,091 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:05:54,092 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 12:05:54,170 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:05:54,170 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 12:05:54,170 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 12:05:54,170 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 12:05:54,170 - INFO - 正在查找微信主窗口...
2025-08-04 12:05:55,129 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 12:05:55,129 - INFO - 正在激活微信窗口...
2025-08-04 12:05:57,642 - INFO - 微信窗口已激活。
2025-08-04 12:05:57,643 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 12:06:04,676 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 12:06:04,677 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 12:06:08,011 - INFO - 正在查找聊天输入框...
2025-08-04 12:06:10,012 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 12:06:10,024 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 12:06:10,025 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 12:06:11,616 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 12:06:13,904 - INFO - 链接已粘贴，正在发送...
2025-08-04 12:06:14,052 - INFO - 找到发送按钮，点击发送...
2025-08-04 12:06:14,863 - INFO - 链接已发送。
2025-08-04 12:06:17,864 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 12:06:19,946 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 12:06:20,680 - INFO - ✅ 成功点击最新链接。
2025-08-04 12:06:23,680 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 12:06:23,681 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:06:23,681 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:06:23,684 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:06:26,392 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:06:27,893 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:06:35,244 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 12:06:35,244 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 12:06:35,244 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 12:06:35,244 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:06:35,244 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:06:35,247 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:06:37,953 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:06:38,454 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:06:38,454 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:06:38,467 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:06:41,172 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:06:42,674 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:06:50,433 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:06:50,434 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:06:52,934 - INFO - 第 1 次刷新完成
2025-08-04 12:06:54,435 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 12:06:54,436 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:06:54,436 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:06:54,447 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:06:57,153 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:06:57,654 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:06:57,655 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:06:57,668 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:07:00,373 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:07:01,874 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:07:09,728 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:07:09,728 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:07:12,229 - INFO - 第 2 次刷新完成
2025-08-04 12:07:13,730 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 12:07:13,731 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:07:13,732 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:07:13,744 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:07:16,449 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:07:16,950 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:07:16,951 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:07:16,963 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:07:19,667 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:07:21,169 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:07:29,237 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:07:29,238 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:07:31,739 - INFO - 第 3 次刷新完成
2025-08-04 12:07:31,740 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 12:07:31,741 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 12:07:31,742 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 12:07:31,742 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 12:07:31,742 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 12:07:32,743 - INFO - 检测到Cookie文件已生成。
2025-08-04 12:07:32,745 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 12:07:32,746 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 12:07:32,746 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-04 12:07:32,747 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 12:07:32,747 - INFO - 正在停止Cookie抓取器 (PID: 16204)...
2025-08-04 12:07:32,748 - INFO - Cookie抓取器已成功终止。
2025-08-04 12:07:32,748 - INFO - 正在验证并清理代理设置...
2025-08-04 12:07:32,749 - INFO - === 开始重置网络状态 ===
2025-08-04 12:07:32,749 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:07:32,857 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:07:32,858 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:07:32,858 - INFO - 系统代理已成功关闭
2025-08-04 12:07:32,858 - INFO - ✅ 代理关闭操作
2025-08-04 12:07:32,858 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:07:34,175 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:07:34,175 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:07:34,175 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 12:07:34,973 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:07:34,973 - INFO - ✅ 网络连接验证正常
2025-08-04 12:07:37,974 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 12:07:37,975 - INFO - [步骤 5/5] 正在启动批量文章爬取...
2025-08-04 12:10:36,290 - INFO - 🎉 抓取完成！结果已保存到 ./data/readnum_batch/readnum_batch_20250804_121036.xlsx 和 ./data/readnum_batch/readnum_batch_20250804_121036.json
2025-08-04 12:10:36,290 - INFO - ================================================================================
2025-08-04 12:10:36,291 - INFO - ✅ 全新自动化流程执行完毕 ✅
2025-08-04 12:10:36,291 - INFO - ================================================================================
2025-08-04 12:51:00,112 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 12:51:00,113 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:51:00,117 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:51:00,117 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:51:00,189 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:51:00,189 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:51:00,189 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:51:27,129 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 12:51:27,130 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:51:27,134 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:51:27,204 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:51:27,204 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:51:27,204 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:51:27,205 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:51:27,208 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:51:27,209 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:51:27,209 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:51:57,842 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 12:51:57,842 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:51:57,847 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:51:57,919 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:51:57,919 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:51:57,919 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:52:24,674 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 12:52:24,674 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 12:52:24,679 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 12:52:24,679 - INFO - ================================================================================
2025-08-04 12:52:24,680 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 12:52:24,680 - INFO - ================================================================================
2025-08-04 12:52:24,680 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 12:52:24,750 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 12:52:24,751 - INFO - 找到有效目标 2: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 12:52:24,752 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 12:52:24,752 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 12:52:24,752 - INFO - ============================================================
2025-08-04 12:52:24,752 - INFO - 📍 处理第 1/2 个公众号: 钟山清风
2025-08-04 12:52:24,752 - INFO - ============================================================
2025-08-04 12:52:24,752 - INFO - [步骤 1/5] 为 '钟山清风' 启动 Cookie 抓取器...
2025-08-04 12:52:24,752 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 12:52:24,752 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 12:52:24,752 - INFO - === 开始重置网络状态 ===
2025-08-04 12:52:24,752 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:52:24,856 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:52:24,856 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:52:24,856 - INFO - 系统代理已成功关闭
2025-08-04 12:52:24,857 - INFO - ✅ 代理关闭操作
2025-08-04 12:52:24,857 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:52:25,757 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:52:25,757 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:52:25,758 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 12:52:25,758 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 12:52:25,759 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 12:52:26,266 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 12:52:26,267 - INFO - 🔄 Cookie抓取器进程已启动，PID: 11556
2025-08-04 12:52:26,267 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 12:52:29,268 - INFO - 等待代理服务启动...
2025-08-04 12:52:29,751 - INFO - 代理服务已启动并正常工作
2025-08-04 12:52:29,751 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 11556)
2025-08-04 12:52:29,751 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 12:52:29,752 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-04 12:52:29,752 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:52:29,753 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 12:52:29,753 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 12:52:29,753 - INFO - 正在查找微信主窗口...
2025-08-04 12:52:29,813 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 12:52:29,813 - INFO - 正在激活微信窗口...
2025-08-04 12:52:32,328 - INFO - 微信窗口已激活。
2025-08-04 12:52:32,329 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 12:52:38,948 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 12:52:38,949 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 12:52:42,281 - INFO - 正在查找聊天输入框...
2025-08-04 12:52:44,282 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 12:52:44,289 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 12:52:44,289 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 12:52:45,869 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 12:52:48,158 - INFO - 链接已粘贴，正在发送...
2025-08-04 12:52:48,304 - INFO - 找到发送按钮，点击发送...
2025-08-04 12:52:49,111 - INFO - 链接已发送。
2025-08-04 12:52:52,112 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 12:52:54,202 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 12:52:54,929 - INFO - ✅ 成功点击最新链接。
2025-08-04 12:52:57,930 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 12:52:57,931 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:52:57,932 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:52:57,945 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:00,651 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:02,151 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:09,640 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 12:53:09,640 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 12:53:09,640 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 12:53:09,640 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:09,640 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:09,644 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:12,350 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:12,851 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:12,852 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:12,867 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:15,571 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:17,072 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:24,981 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:53:24,981 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:53:27,482 - INFO - 第 1 次刷新完成
2025-08-04 12:53:28,983 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 12:53:28,983 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:28,984 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:28,994 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:31,699 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:32,200 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:32,201 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:32,213 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:34,921 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:36,423 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:53:44,376 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:53:44,377 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:53:46,878 - INFO - 第 2 次刷新完成
2025-08-04 12:53:48,379 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 12:53:48,380 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:48,380 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:48,389 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:51,094 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:51,596 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:53:51,597 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:53:51,605 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:53:54,311 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:53:55,812 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:54:03,731 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:54:03,732 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:54:06,232 - INFO - 第 3 次刷新完成
2025-08-04 12:54:06,233 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 12:54:06,234 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 12:54:06,235 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-04 12:54:06,235 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 12:54:07,236 - INFO - 检测到Cookie文件已生成。
2025-08-04 12:54:07,238 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 12:54:07,239 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 12:54:07,239 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-04 12:54:07,239 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 12:54:07,240 - INFO - 正在停止Cookie抓取器 (PID: 11556)...
2025-08-04 12:54:07,242 - INFO - Cookie抓取器已成功终止。
2025-08-04 12:54:07,243 - INFO - 正在验证并清理代理设置...
2025-08-04 12:54:07,243 - INFO - === 开始重置网络状态 ===
2025-08-04 12:54:07,243 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:54:07,392 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:54:07,392 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:54:07,393 - INFO - 系统代理已成功关闭
2025-08-04 12:54:07,393 - INFO - ✅ 代理关闭操作
2025-08-04 12:54:07,393 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:54:09,375 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:54:09,375 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:54:09,376 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 12:54:10,707 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:54:10,708 - INFO - ✅ 网络连接验证正常
2025-08-04 12:54:13,709 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 12:54:13,710 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-04 12:57:20,114 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 18 篇文章
2025-08-04 12:57:20,114 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250804_125720.xlsx
2025-08-04 12:57:20,114 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 12:57:35,114 - INFO - ============================================================
2025-08-04 12:57:35,115 - INFO - 📍 处理第 2/2 个公众号: 金陵档案
2025-08-04 12:57:35,115 - INFO - ============================================================
2025-08-04 12:57:35,116 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 12:57:35,116 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 12:57:35,116 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 12:57:35,116 - INFO - === 开始重置网络状态 ===
2025-08-04 12:57:35,116 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:57:35,204 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:57:35,204 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:57:35,205 - INFO - 系统代理已成功关闭
2025-08-04 12:57:35,205 - INFO - ✅ 代理关闭操作
2025-08-04 12:57:35,205 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:57:37,878 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:57:37,879 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:57:37,879 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 12:57:37,880 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 12:57:37,881 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 12:57:38,420 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 12:57:38,421 - INFO - 🔄 Cookie抓取器进程已启动，PID: 37452
2025-08-04 12:57:38,421 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 12:57:41,421 - INFO - 等待代理服务启动...
2025-08-04 12:57:43,235 - INFO - 代理服务已启动并正常工作
2025-08-04 12:57:43,235 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 37452)
2025-08-04 12:57:43,235 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 12:57:43,235 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 12:57:43,235 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 12:57:43,236 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 12:57:43,236 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 12:57:43,236 - INFO - 正在查找微信主窗口...
2025-08-04 12:57:43,477 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 12:57:43,477 - INFO - 正在激活微信窗口...
2025-08-04 12:57:45,983 - INFO - 微信窗口已激活。
2025-08-04 12:57:45,984 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 12:57:52,696 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 12:57:52,696 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 12:57:56,031 - INFO - 正在查找聊天输入框...
2025-08-04 12:57:58,032 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 12:57:58,037 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 12:57:58,037 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 12:57:59,607 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&mid=2247539053&idx=1&sn=9dcae8e371bf87efbe121cf3ed8afee7&chksm=cf1b71bdf86cf8abd355f4b297ae51941771cc98fc1eb481b875e64b6bee30ac39c4f06a4133#rd
2025-08-04 12:58:01,896 - INFO - 链接已粘贴，正在发送...
2025-08-04 12:58:02,030 - INFO - 找到发送按钮，点击发送...
2025-08-04 12:58:02,845 - INFO - 链接已发送。
2025-08-04 12:58:05,847 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 12:58:07,935 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 12:58:08,659 - INFO - ✅ 成功点击最新链接。
2025-08-04 12:58:11,660 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 12:58:11,661 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:11,663 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:11,671 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:14,377 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:15,879 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:23,257 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 12:58:23,258 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 12:58:23,258 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 12:58:23,258 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:23,258 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:23,261 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:25,968 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:26,469 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:26,470 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:26,483 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:29,193 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:30,693 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:38,594 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:58:38,595 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:58:41,096 - INFO - 第 1 次刷新完成
2025-08-04 12:58:42,597 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 12:58:42,598 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:42,598 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:42,606 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:45,312 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:45,814 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:58:45,815 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:58:45,826 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:58:48,532 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:58:50,033 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:58:57,999 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:58:57,999 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:59:00,500 - INFO - 第 2 次刷新完成
2025-08-04 12:59:02,001 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 12:59:02,001 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:59:02,001 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:59:02,005 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:59:04,712 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:59:05,213 - INFO - 正在查找微信浏览器窗口...
2025-08-04 12:59:05,213 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 12:59:05,225 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 12:59:07,932 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 12:59:09,433 - INFO - 正在检测SSL证书错误页面...
2025-08-04 12:59:17,341 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 12:59:17,342 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 12:59:19,843 - INFO - 第 3 次刷新完成
2025-08-04 12:59:19,843 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 12:59:19,844 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 12:59:19,844 - INFO - [步骤 3/5] 等待 '金陵档案' 的 Cookie 数据...
2025-08-04 12:59:19,845 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 12:59:20,846 - INFO - 检测到Cookie文件已生成。
2025-08-04 12:59:20,847 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 12:59:20,847 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 12:59:20,848 - INFO - [步骤 4/5] 停止 '金陵档案' 的 Cookie 抓取器...
2025-08-04 12:59:20,848 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 12:59:20,848 - INFO - 正在停止Cookie抓取器 (PID: 37452)...
2025-08-04 12:59:20,850 - INFO - Cookie抓取器已成功终止。
2025-08-04 12:59:20,850 - INFO - 正在验证并清理代理设置...
2025-08-04 12:59:20,850 - INFO - === 开始重置网络状态 ===
2025-08-04 12:59:20,851 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 12:59:20,954 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 12:59:20,954 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 12:59:20,954 - INFO - 系统代理已成功关闭
2025-08-04 12:59:20,954 - INFO - ✅ 代理关闭操作
2025-08-04 12:59:20,954 - INFO - 🔗 正在验证网络连接...
2025-08-04 12:59:22,476 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:59:22,476 - INFO - ✅ 网络状态重置验证完成
2025-08-04 12:59:22,476 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 12:59:23,463 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 12:59:23,464 - INFO - ✅ 网络连接验证正常
2025-08-04 12:59:26,464 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 12:59:26,465 - INFO - [步骤 5/5] 开始爬取 '金陵档案' 的文章...
2025-08-04 12:59:26,611 - WARNING - ⚠️ 公众号 '金陵档案' 未获取到任何文章数据
2025-08-04 12:59:26,611 - INFO - ================================================================================
2025-08-04 12:59:26,611 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 12:59:26,612 - INFO - ================================================================================
2025-08-04 12:59:26,612 - INFO - ✅ 成功处理: 1 个公众号
2025-08-04 12:59:26,612 - INFO - ❌ 失败处理: 1 个公众号
2025-08-04 12:59:26,612 - INFO - 📄 总计文章: 18 篇
2025-08-04 12:59:26,643 - INFO - 🎉 汇总数据已保存到:
2025-08-04 12:59:26,644 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_125926.xlsx
2025-08-04 12:59:26,644 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_125926.json
2025-08-04 12:59:26,644 - INFO - ================================================================================
2025-08-04 12:59:26,644 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 12:59:26,644 - INFO - ================================================================================
2025-08-04 13:12:10,208 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 13:12:10,217 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 13:12:10,234 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 13:12:10,234 - INFO - ================================================================================
2025-08-04 13:12:10,235 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 13:12:10,235 - INFO - ================================================================================
2025-08-04 13:12:10,235 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 13:12:10,699 - INFO - 找到有效目标 1: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 13:12:10,700 - INFO - 找到有效目标 2: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 13:12:10,700 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 13:12:10,701 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 13:12:10,701 - INFO - ============================================================
2025-08-04 13:12:10,701 - INFO - 📍 处理第 1/2 个公众号: 金陵档案
2025-08-04 13:12:10,702 - INFO - ============================================================
2025-08-04 13:12:10,702 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 13:12:10,702 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 13:12:10,703 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 13:12:10,703 - INFO - === 开始重置网络状态 ===
2025-08-04 13:12:10,703 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 13:12:10,805 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 13:12:10,805 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 13:12:10,805 - INFO - 系统代理已成功关闭
2025-08-04 13:12:10,805 - INFO - ✅ 代理关闭操作
2025-08-04 13:12:10,805 - INFO - 🔗 正在验证网络连接...
2025-08-04 13:12:11,808 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:12:11,809 - INFO - ✅ 网络状态重置验证完成
2025-08-04 13:12:11,810 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 13:12:11,811 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 13:12:11,812 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 13:12:14,100 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 13:12:14,101 - INFO - 🔄 Cookie抓取器进程已启动，PID: 36724
2025-08-04 13:12:14,101 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 13:12:17,101 - INFO - 等待代理服务启动...
2025-08-04 13:12:17,584 - INFO - 代理服务已启动并正常工作
2025-08-04 13:12:17,584 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 36724)
2025-08-04 13:12:17,584 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 13:12:17,585 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 13:12:17,585 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 13:12:17,585 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 13:12:17,586 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 13:12:17,586 - INFO - 正在查找微信主窗口...
2025-08-04 13:12:19,801 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 13:12:19,801 - INFO - 正在激活微信窗口...
2025-08-04 13:12:22,312 - INFO - 微信窗口已激活。
2025-08-04 13:12:22,313 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 13:12:29,335 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 13:12:29,336 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 13:12:32,665 - INFO - 正在查找聊天输入框...
2025-08-04 13:12:34,666 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 13:12:34,672 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 13:12:34,672 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 13:12:36,256 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&mid=2247539053&idx=1&sn=9dcae8e371bf87efbe121cf3ed8afee7&chksm=cf1b71bdf86cf8abd355f4b297ae51941771cc98fc1eb481b875e64b6bee30ac39c4f06a4133#rd
2025-08-04 13:12:38,549 - INFO - 链接已粘贴，正在发送...
2025-08-04 13:12:38,693 - INFO - 找到发送按钮，点击发送...
2025-08-04 13:12:39,497 - INFO - 链接已发送。
2025-08-04 13:12:42,498 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 13:12:44,584 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 13:12:45,314 - INFO - ✅ 成功点击最新链接。
2025-08-04 13:12:48,315 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 13:12:48,315 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:12:48,316 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:12:48,328 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:12:51,036 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:12:52,537 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:13:00,083 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 13:13:00,083 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 13:13:00,083 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 13:13:00,083 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:00,084 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:00,087 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:02,794 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:03,295 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:03,296 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:03,305 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:06,011 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:07,512 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:13:15,619 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:13:15,620 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:13:18,120 - INFO - 第 1 次刷新完成
2025-08-04 13:13:19,622 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 13:13:19,623 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:19,623 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:19,634 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:22,340 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:22,841 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:22,841 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:22,848 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:25,552 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:27,054 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:13:35,161 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:13:35,162 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:13:37,663 - INFO - 第 2 次刷新完成
2025-08-04 13:13:39,164 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 13:13:39,164 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:39,164 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:39,168 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:41,878 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:42,379 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:13:42,380 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:13:42,389 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:13:45,098 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:13:46,598 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:13:54,716 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:13:54,716 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:13:57,217 - INFO - 第 3 次刷新完成
2025-08-04 13:13:57,218 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 13:13:57,219 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 13:13:57,219 - INFO - [步骤 3/5] 等待 '金陵档案' 的 Cookie 数据...
2025-08-04 13:13:57,219 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 13:13:58,220 - INFO - 检测到Cookie文件已生成。
2025-08-04 13:13:58,222 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 13:13:58,223 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 13:13:58,223 - INFO - [步骤 4/5] 停止 '金陵档案' 的 Cookie 抓取器...
2025-08-04 13:13:58,224 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 13:13:58,224 - INFO - 正在停止Cookie抓取器 (PID: 36724)...
2025-08-04 13:13:58,227 - INFO - Cookie抓取器已成功终止。
2025-08-04 13:13:58,227 - INFO - 正在验证并清理代理设置...
2025-08-04 13:13:58,228 - INFO - === 开始重置网络状态 ===
2025-08-04 13:13:58,228 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 13:13:58,350 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 13:13:58,350 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 13:13:58,350 - INFO - 系统代理已成功关闭
2025-08-04 13:13:58,350 - INFO - ✅ 代理关闭操作
2025-08-04 13:13:58,351 - INFO - 🔗 正在验证网络连接...
2025-08-04 13:14:00,362 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:14:00,363 - INFO - ✅ 网络状态重置验证完成
2025-08-04 13:14:00,363 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 13:14:01,302 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:14:01,302 - INFO - ✅ 网络连接验证正常
2025-08-04 13:14:04,303 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 13:14:04,303 - INFO - [步骤 5/5] 开始爬取 '金陵档案' 的文章...
2025-08-04 13:14:25,217 - INFO - ✅ 公众号 '金陵档案' 爬取完成！获取 2 篇文章
2025-08-04 13:14:25,217 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_金陵档案_20250804_131425.xlsx
2025-08-04 13:14:25,217 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 13:14:40,218 - INFO - ============================================================
2025-08-04 13:14:40,218 - INFO - 📍 处理第 2/2 个公众号: 钟山清风
2025-08-04 13:14:40,218 - INFO - ============================================================
2025-08-04 13:14:40,219 - INFO - [步骤 1/5] 为 '钟山清风' 启动 Cookie 抓取器...
2025-08-04 13:14:40,219 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 13:14:40,219 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 13:14:40,219 - INFO - === 开始重置网络状态 ===
2025-08-04 13:14:40,219 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 13:14:40,303 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 13:14:40,303 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 13:14:40,303 - INFO - 系统代理已成功关闭
2025-08-04 13:14:40,304 - INFO - ✅ 代理关闭操作
2025-08-04 13:14:40,304 - INFO - 🔗 正在验证网络连接...
2025-08-04 13:14:42,289 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:14:42,289 - INFO - ✅ 网络状态重置验证完成
2025-08-04 13:14:42,290 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 13:14:42,290 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 13:14:42,290 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 13:14:42,780 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 13:14:42,782 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21276
2025-08-04 13:14:42,782 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 13:14:45,783 - INFO - 等待代理服务启动...
2025-08-04 13:14:46,261 - INFO - 代理服务已启动并正常工作
2025-08-04 13:14:46,262 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21276)
2025-08-04 13:14:46,263 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 13:14:46,263 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-04 13:14:46,263 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 13:14:46,264 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 13:14:46,265 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 13:14:46,265 - INFO - 正在查找微信主窗口...
2025-08-04 13:14:46,717 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 13:14:46,717 - INFO - 正在激活微信窗口...
2025-08-04 13:14:49,231 - INFO - 微信窗口已激活。
2025-08-04 13:14:49,231 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 13:14:56,265 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 13:14:56,265 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 13:14:59,593 - INFO - 正在查找聊天输入框...
2025-08-04 13:15:01,594 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 13:15:01,605 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 13:15:01,605 - INFO - 点击聊天输入区域坐标: (504, 657)
2025-08-04 13:15:03,184 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 13:15:05,474 - INFO - 链接已粘贴，正在发送...
2025-08-04 13:15:05,625 - INFO - 找到发送按钮，点击发送...
2025-08-04 13:15:06,430 - INFO - 链接已发送。
2025-08-04 13:15:09,432 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 13:15:11,525 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 13:15:12,260 - INFO - ✅ 成功点击最新链接。
2025-08-04 13:15:15,261 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 13:15:15,261 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:15:15,262 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:15:15,267 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:15:17,971 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:15:19,472 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:15:26,995 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 13:15:26,995 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 13:15:26,995 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 13:15:26,995 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:15:26,996 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:15:26,999 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:15:29,704 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:15:30,205 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:15:30,206 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:15:30,216 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:15:32,922 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:15:34,422 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:15:42,459 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:15:42,461 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:15:44,962 - INFO - 第 1 次刷新完成
2025-08-04 13:15:46,463 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 13:15:46,463 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:15:46,463 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:15:46,470 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:15:49,174 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:15:49,676 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:15:49,677 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:15:49,687 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:15:52,392 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:15:53,894 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:16:02,029 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:16:02,029 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:16:04,530 - INFO - 第 2 次刷新完成
2025-08-04 13:16:06,031 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 13:16:06,032 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:16:06,033 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:16:06,044 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:16:08,750 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:16:09,251 - INFO - 正在查找微信浏览器窗口...
2025-08-04 13:16:09,252 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 13:16:09,265 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 13:16:11,970 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 13:16:13,471 - INFO - 正在检测SSL证书错误页面...
2025-08-04 13:16:21,609 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 13:16:21,610 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 13:16:24,111 - INFO - 第 3 次刷新完成
2025-08-04 13:16:24,112 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 13:16:24,113 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 13:16:24,113 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-04 13:16:24,113 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 13:16:25,115 - INFO - 检测到Cookie文件已生成。
2025-08-04 13:16:25,116 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 13:16:25,116 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 13:16:25,116 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-04 13:16:25,117 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 13:16:25,117 - INFO - 正在停止Cookie抓取器 (PID: 21276)...
2025-08-04 13:16:25,119 - INFO - Cookie抓取器已成功终止。
2025-08-04 13:16:25,120 - INFO - 正在验证并清理代理设置...
2025-08-04 13:16:25,120 - INFO - === 开始重置网络状态 ===
2025-08-04 13:16:25,120 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 13:16:25,258 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 13:16:25,258 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 13:16:25,258 - INFO - 系统代理已成功关闭
2025-08-04 13:16:25,259 - INFO - ✅ 代理关闭操作
2025-08-04 13:16:25,259 - INFO - 🔗 正在验证网络连接...
2025-08-04 13:16:26,179 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:16:26,179 - INFO - ✅ 网络状态重置验证完成
2025-08-04 13:16:26,179 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 13:16:29,116 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 13:16:29,116 - INFO - ✅ 网络连接验证正常
2025-08-04 13:16:32,117 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 13:16:32,118 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-04 13:19:19,177 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 18 篇文章
2025-08-04 13:19:19,177 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250804_131919.xlsx
2025-08-04 13:19:19,177 - INFO - ================================================================================
2025-08-04 13:19:19,177 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 13:19:19,177 - INFO - ================================================================================
2025-08-04 13:19:19,177 - INFO - ✅ 成功处理: 2 个公众号
2025-08-04 13:19:19,178 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 13:19:19,178 - INFO - 📄 总计文章: 20 篇
2025-08-04 13:19:19,193 - INFO - 🎉 汇总数据已保存到:
2025-08-04 13:19:19,193 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_131919.xlsx
2025-08-04 13:19:19,194 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_131919.json
2025-08-04 13:19:19,194 - INFO - ================================================================================
2025-08-04 13:19:19,194 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 13:19:19,194 - INFO - ================================================================================
2025-08-04 15:01:08,070 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:01:08,076 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:01:08,093 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:01:08,093 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:08,093 - INFO - ================================================================================
2025-08-04 15:01:08,093 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:01:08,093 - INFO - ================================================================================
2025-08-04 15:01:08,093 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:01:08,730 - INFO - 找到有效目标 1: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 15:01:08,730 - INFO - 找到有效目标 2: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 15:01:08,730 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:01:08,730 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:01:08,730 - INFO - ============================================================
2025-08-04 15:01:08,731 - INFO - 📍 处理第 1/2 个公众号: 金陵档案
2025-08-04 15:01:08,731 - INFO - ============================================================
2025-08-04 15:01:08,731 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 15:01:08,731 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:01:08,731 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:01:08,731 - INFO - === 开始重置网络状态 ===
2025-08-04 15:01:08,731 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:01:08,857 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:01:08,857 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:01:08,858 - INFO - 系统代理已成功关闭
2025-08-04 15:01:08,858 - INFO - ✅ 代理关闭操作
2025-08-04 15:01:08,858 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:01:10,375 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:01:10,376 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:01:10,376 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:01:10,376 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:01:10,377 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:01:12,859 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:01:12,860 - INFO - 🔄 Cookie抓取器进程已启动，PID: 12964
2025-08-04 15:01:12,860 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:01:15,860 - INFO - 等待代理服务启动...
2025-08-04 15:01:16,320 - INFO - 代理服务已启动并正常工作
2025-08-04 15:01:16,320 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 12964)
2025-08-04 15:01:16,320 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:01:16,320 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 15:01:16,320 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:16,321 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:01:16,321 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:01:16,321 - INFO - 正在查找微信主窗口...
2025-08-04 15:01:16,404 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:01:16,405 - INFO - 正在激活微信窗口...
2025-08-04 15:01:18,920 - INFO - 微信窗口已激活。
2025-08-04 15:01:18,921 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:01:25,931 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:01:25,932 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:01:29,264 - INFO - 正在查找聊天输入框...
2025-08-04 15:01:38,346 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:01:38,346 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:01:38,351 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:01:38,351 - INFO - ================================================================================
2025-08-04 15:01:38,351 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:01:38,351 - INFO - ================================================================================
2025-08-04 15:01:38,351 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:01:38,421 - INFO - 找到有效目标 1: 金陵档案 - https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&...
2025-08-04 15:01:38,422 - INFO - 找到有效目标 2: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 15:01:38,422 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:01:38,423 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:01:38,423 - INFO - ============================================================
2025-08-04 15:01:38,423 - INFO - 📍 处理第 1/2 个公众号: 金陵档案
2025-08-04 15:01:38,423 - INFO - ============================================================
2025-08-04 15:01:38,423 - INFO - [步骤 1/5] 为 '金陵档案' 启动 Cookie 抓取器...
2025-08-04 15:01:38,423 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:01:38,423 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:01:38,423 - INFO - === 开始重置网络状态 ===
2025-08-04 15:01:38,423 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:01:38,508 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:01:38,508 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:01:38,508 - INFO - 系统代理已成功关闭
2025-08-04 15:01:38,508 - INFO - ✅ 代理关闭操作
2025-08-04 15:01:38,509 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:01:39,446 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:01:39,446 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:01:39,446 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:01:39,446 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:01:39,446 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:01:39,936 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:01:39,937 - INFO - 🔄 Cookie抓取器进程已启动，PID: 29548
2025-08-04 15:01:39,937 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:01:42,938 - INFO - 等待代理服务启动...
2025-08-04 15:01:43,418 - INFO - 代理服务已启动并正常工作
2025-08-04 15:01:43,419 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 29548)
2025-08-04 15:01:43,419 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:01:43,420 - INFO - [步骤 2/5] 为 '金陵档案' 启动 UI 自动化...
2025-08-04 15:01:43,420 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:01:43,420 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:01:43,421 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:01:43,421 - INFO - 正在查找微信主窗口...
2025-08-04 15:01:43,491 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:01:43,491 - INFO - 正在激活微信窗口...
2025-08-04 15:01:46,005 - INFO - 微信窗口已激活。
2025-08-04 15:01:46,006 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:01:53,034 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:01:53,035 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:01:56,368 - INFO - 正在查找聊天输入框...
2025-08-04 15:01:58,369 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:01:58,382 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:01:58,382 - INFO - 点击聊天输入区域坐标: (423, 717)
2025-08-04 15:01:59,992 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3ODA2ODE1OQ==&mid=2247539053&idx=1&sn=9dcae8e371bf87efbe121cf3ed8afee7&chksm=cf1b71bdf86cf8abd355f4b297ae51941771cc98fc1eb481b875e64b6bee30ac39c4f06a4133#rd
2025-08-04 15:02:02,283 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:02:02,445 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:02:03,249 - INFO - 链接已发送。
2025-08-04 15:02:06,250 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:02:08,335 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:02:09,068 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:02:12,068 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:02:12,069 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:02:12,069 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:02:12,077 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:02:14,784 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:02:16,285 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:02:23,202 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:02:23,202 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:02:23,202 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:02:23,203 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:02:23,203 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:02:23,212 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:05:59,584 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:05:59,585 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:05:59,589 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:05:59,590 - INFO - ================================================================================
2025-08-04 15:05:59,590 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:05:59,590 - INFO - ================================================================================
2025-08-04 15:05:59,590 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:05:59,660 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:05:59,660 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:05:59,661 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:05:59,661 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:05:59,661 - INFO - ============================================================
2025-08-04 15:05:59,661 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:05:59,661 - INFO - ============================================================
2025-08-04 15:05:59,661 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:05:59,662 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:05:59,662 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:05:59,662 - INFO - === 开始重置网络状态 ===
2025-08-04 15:05:59,662 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:05:59,750 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:05:59,751 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:05:59,751 - INFO - 系统代理已成功关闭
2025-08-04 15:05:59,751 - INFO - ✅ 代理关闭操作
2025-08-04 15:05:59,751 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:06:00,695 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:06:00,696 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:06:00,696 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:06:00,697 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:06:00,697 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:06:01,191 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:06:01,192 - INFO - 🔄 Cookie抓取器进程已启动，PID: 40100
2025-08-04 15:06:01,192 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:06:04,192 - INFO - 等待代理服务启动...
2025-08-04 15:06:04,814 - INFO - 代理服务已启动并正常工作
2025-08-04 15:06:04,815 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 40100)
2025-08-04 15:06:04,816 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:06:04,816 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:06:04,817 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:06:04,818 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:06:04,818 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:06:04,818 - INFO - 正在查找微信主窗口...
2025-08-04 15:06:04,885 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:06:04,886 - INFO - 正在激活微信窗口...
2025-08-04 15:06:07,399 - INFO - 微信窗口已激活。
2025-08-04 15:06:07,400 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:06:14,136 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:06:14,137 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:06:17,467 - INFO - 正在查找聊天输入框...
2025-08-04 15:06:19,468 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:06:19,479 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:06:19,480 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:06:21,084 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:06:23,371 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:06:23,538 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:06:24,349 - INFO - 链接已发送。
2025-08-04 15:06:27,351 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:06:29,432 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:06:30,168 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:06:33,169 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:06:33,169 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:33,170 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:33,179 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:35,886 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:37,388 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:06:45,558 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:06:45,558 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:06:45,558 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:06:45,558 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:45,558 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:45,562 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:48,268 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:48,769 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:06:48,769 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:06:48,780 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:06:51,485 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:06:52,986 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:01,619 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:01,620 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:04,120 - INFO - 第 1 次刷新完成
2025-08-04 15:07:05,621 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:07:05,622 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:05,622 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:05,634 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:08,340 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:08,841 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:08,842 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:08,854 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:11,559 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:13,060 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:21,708 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:21,709 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:24,210 - INFO - 第 2 次刷新完成
2025-08-04 15:07:25,711 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:07:25,712 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:25,713 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:25,724 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:28,431 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:28,932 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:07:28,933 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:07:28,942 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:07:31,648 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:07:33,150 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:07:41,789 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:07:41,790 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:07:44,290 - INFO - 第 3 次刷新完成
2025-08-04 15:07:44,291 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:07:44,292 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:07:44,292 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:07:44,292 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:08:29,311 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:08:29,313 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:08:29,313 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:08:29,314 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:08:29,314 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:08:29,315 - INFO - 正在停止Cookie抓取器 (PID: 40100)...
2025-08-04 15:08:29,316 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:08:29,317 - INFO - 正在验证并清理代理设置...
2025-08-04 15:08:29,317 - INFO - === 开始重置网络状态 ===
2025-08-04 15:08:29,317 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:08:29,415 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:08:29,416 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:08:29,416 - INFO - 系统代理已成功关闭
2025-08-04 15:08:29,416 - INFO - ✅ 代理关闭操作
2025-08-04 15:08:29,416 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:08:30,427 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:08:30,427 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:08:30,428 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:08:31,509 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:08:31,509 - INFO - ✅ 网络连接验证正常
2025-08-04 15:08:34,510 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:08:34,510 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-04 15:16:44,857 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:16:44,857 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:16:44,885 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:16:44,885 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:16:44,885 - INFO - ================================================================================
2025-08-04 15:16:44,885 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:16:44,885 - INFO - ================================================================================
2025-08-04 15:16:44,886 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:16:44,963 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:16:44,964 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:16:44,964 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:16:44,964 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:16:44,964 - INFO - ============================================================
2025-08-04 15:16:44,964 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:16:44,964 - INFO - ============================================================
2025-08-04 15:16:44,964 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:16:44,965 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:16:44,965 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:16:44,965 - INFO - === 开始重置网络状态 ===
2025-08-04 15:16:44,965 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:16:45,047 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:16:45,047 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:16:45,047 - INFO - 系统代理已成功关闭
2025-08-04 15:16:45,048 - INFO - ✅ 代理关闭操作
2025-08-04 15:16:45,048 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:16:46,360 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:16:46,361 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:16:46,361 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:16:46,362 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:16:46,363 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:16:46,859 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:16:46,860 - INFO - 🔄 Cookie抓取器进程已启动，PID: 4644
2025-08-04 15:16:46,860 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:16:49,860 - INFO - 等待代理服务启动...
2025-08-04 15:16:50,345 - INFO - 代理服务已启动并正常工作
2025-08-04 15:16:50,345 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 4644)
2025-08-04 15:16:50,346 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:16:50,346 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:16:50,347 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:16:50,348 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:16:50,348 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:16:50,349 - INFO - 正在查找微信主窗口...
2025-08-04 15:16:50,420 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:16:50,421 - INFO - 正在激活微信窗口...
2025-08-04 15:16:52,929 - INFO - 微信窗口已激活。
2025-08-04 15:16:52,930 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:16:59,683 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:16:59,684 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:17:03,017 - INFO - 正在查找聊天输入框...
2025-08-04 15:17:05,017 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:17:05,030 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:17:05,030 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:17:06,606 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:17:08,892 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:17:09,070 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:17:10,067 - INFO - 链接已发送。
2025-08-04 15:17:13,068 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:17:15,161 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:17:15,900 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:17:18,901 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:17:18,902 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:18,902 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:18,910 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:17:21,616 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:17:23,117 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:17:31,318 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:17:31,318 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:17:31,318 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:17:31,318 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:31,318 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:31,328 - INFO - 尝试传统窗口查找方法...
2025-08-04 15:17:35,419 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 15:17:37,233 - INFO - 已成功激活浏览器窗口
2025-08-04 15:17:37,734 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:37,735 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:37,746 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:17:40,452 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:17:41,953 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:17:50,642 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:17:50,643 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:17:53,144 - INFO - 第 1 次刷新完成
2025-08-04 15:17:54,644 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:17:54,645 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:17:54,646 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:17:54,667 - INFO - 尝试传统窗口查找方法...
2025-08-04 15:18:01,111 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 15:18:02,923 - INFO - 已成功激活浏览器窗口
2025-08-04 15:18:03,424 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:03,424 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:03,438 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:06,143 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:07,644 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:18:16,389 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:18:16,390 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:18:18,891 - INFO - 第 2 次刷新完成
2025-08-04 15:18:20,392 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:18:20,393 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:20,394 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:20,408 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:23,114 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:23,615 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:18:23,616 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:18:23,633 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:18:26,339 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:18:27,840 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:18:36,543 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:18:36,544 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:18:39,045 - INFO - 第 3 次刷新完成
2025-08-04 15:18:39,046 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:18:39,048 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:18:39,049 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:18:39,050 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:28:15,921 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:28:15,921 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:28:15,926 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:28:15,926 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:28:15,926 - INFO - ================================================================================
2025-08-04 15:28:15,926 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:28:15,926 - INFO - ================================================================================
2025-08-04 15:28:15,926 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:28:16,003 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:28:16,003 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:28:16,003 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:28:16,003 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:28:16,003 - INFO - ============================================================
2025-08-04 15:28:16,003 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:28:16,004 - INFO - ============================================================
2025-08-04 15:28:16,004 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:28:16,004 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:28:16,004 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:28:16,004 - INFO - === 开始重置网络状态 ===
2025-08-04 15:28:16,004 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:28:16,092 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:28:16,092 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:28:16,092 - INFO - 系统代理已成功关闭
2025-08-04 15:28:16,092 - INFO - ✅ 代理关闭操作
2025-08-04 15:28:16,092 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:28:17,061 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:28:17,061 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:28:17,061 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:28:17,062 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:28:17,062 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:28:17,559 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:28:17,560 - INFO - 🔄 Cookie抓取器进程已启动，PID: 17292
2025-08-04 15:28:17,560 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:28:20,561 - INFO - 等待代理服务启动...
2025-08-04 15:28:21,433 - INFO - 代理服务已启动并正常工作
2025-08-04 15:28:21,433 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 17292)
2025-08-04 15:28:21,434 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:28:21,434 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:28:21,435 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:28:21,436 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:28:21,436 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:28:21,437 - INFO - 正在查找微信主窗口...
2025-08-04 15:28:23,450 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:28:23,450 - INFO - 正在激活微信窗口...
2025-08-04 15:28:25,962 - INFO - 微信窗口已激活。
2025-08-04 15:28:25,962 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:28:32,737 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:28:32,737 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:28:36,061 - INFO - 正在查找聊天输入框...
2025-08-04 15:28:38,062 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:28:38,075 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:28:38,076 - INFO - 点击聊天输入区域坐标: (736, 676)
2025-08-04 15:28:39,669 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:28:41,956 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:28:42,123 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:28:42,932 - INFO - 链接已发送。
2025-08-04 15:28:45,933 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:28:48,030 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:28:48,766 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:28:51,767 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:28:51,767 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:28:51,768 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:28:51,781 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:28:54,487 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:28:55,988 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:04,368 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:29:04,368 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:29:04,369 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:29:04,369 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:04,369 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:04,371 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:07,077 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:07,578 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:07,579 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:07,587 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:10,291 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:11,792 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:20,577 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:29:20,578 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:29:23,078 - INFO - 第 1 次刷新完成
2025-08-04 15:29:24,579 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:29:24,580 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:24,581 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:24,589 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:27,294 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:27,795 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:27,796 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:27,807 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:30,514 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:32,016 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:29:40,725 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:29:40,726 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:29:43,227 - INFO - 第 2 次刷新完成
2025-08-04 15:29:44,728 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:29:44,729 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:44,729 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:44,735 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:47,440 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:47,942 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:29:47,942 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:29:47,951 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:29:50,655 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:29:52,156 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:30:00,887 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:30:00,888 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:30:03,388 - INFO - 第 3 次刷新完成
2025-08-04 15:30:03,389 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:30:03,390 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:30:03,391 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:30:03,391 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:30:04,392 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:30:04,394 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:30:04,395 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:30:04,395 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:30:04,396 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:30:04,397 - INFO - 正在停止Cookie抓取器 (PID: 17292)...
2025-08-04 15:30:04,400 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:30:04,400 - INFO - 正在验证并清理代理设置...
2025-08-04 15:30:04,401 - INFO - === 开始重置网络状态 ===
2025-08-04 15:30:04,401 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:30:04,527 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:30:04,527 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:30:04,527 - INFO - 系统代理已成功关闭
2025-08-04 15:30:04,527 - INFO - ✅ 代理关闭操作
2025-08-04 15:30:04,527 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:30:06,720 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:30:06,720 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:30:06,720 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:30:09,334 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:30:09,334 - INFO - ✅ 网络连接验证正常
2025-08-04 15:30:12,334 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:30:12,335 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-04 15:34:08,484 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:34:08,484 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:34:08,489 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:34:08,489 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:34:08,489 - INFO - ================================================================================
2025-08-04 15:34:08,489 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:34:08,489 - INFO - ================================================================================
2025-08-04 15:34:08,489 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:34:08,565 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:34:08,565 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:34:08,565 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:34:08,566 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:34:08,566 - INFO - ============================================================
2025-08-04 15:34:08,566 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:34:08,566 - INFO - ============================================================
2025-08-04 15:34:08,566 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:34:08,566 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:34:08,566 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:34:08,566 - INFO - === 开始重置网络状态 ===
2025-08-04 15:34:08,566 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:34:08,654 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:34:08,654 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:34:08,655 - INFO - 系统代理已成功关闭
2025-08-04 15:34:08,655 - INFO - ✅ 代理关闭操作
2025-08-04 15:34:08,655 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:34:09,949 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:09,950 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:34:09,950 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:34:09,950 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:34:09,950 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:34:10,456 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:34:10,457 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21896
2025-08-04 15:34:10,457 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:34:13,457 - INFO - 等待代理服务启动...
2025-08-04 15:34:14,974 - INFO - 代理服务已启动并正常工作
2025-08-04 15:34:14,975 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21896)
2025-08-04 15:34:14,975 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:34:14,975 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:34:14,976 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:34:14,977 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:34:14,977 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:34:14,977 - INFO - 正在查找微信主窗口...
2025-08-04 15:34:15,642 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:34:15,642 - INFO - 正在激活微信窗口...
2025-08-04 15:34:18,156 - INFO - 微信窗口已激活。
2025-08-04 15:34:18,156 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:34:25,072 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:34:25,072 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:34:26,840 - ERROR - 查找或进入'文件传输助手'失败: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
2025-08-04 15:34:26,841 - ERROR - 发送链接失败，流程中止。
2025-08-04 15:34:26,841 - ERROR - ❌ 公众号 '南京党建' UI 自动化触发失败，跳过此公众号
2025-08-04 15:34:26,842 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:34:26,842 - INFO - 正在停止Cookie抓取器 (PID: 21896)...
2025-08-04 15:34:26,844 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:34:26,844 - INFO - 正在验证并清理代理设置...
2025-08-04 15:34:26,844 - INFO - === 开始重置网络状态 ===
2025-08-04 15:34:26,845 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:34:26,933 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:34:26,933 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:34:26,934 - INFO - 系统代理已成功关闭
2025-08-04 15:34:26,934 - INFO - ✅ 代理关闭操作
2025-08-04 15:34:26,934 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:34:29,686 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:29,686 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:34:29,686 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:34:32,519 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:32,520 - INFO - ✅ 网络连接验证正常
2025-08-04 15:34:32,520 - INFO - ============================================================
2025-08-04 15:34:32,520 - INFO - 📍 处理第 2/2 个公众号: 南京警方
2025-08-04 15:34:32,521 - INFO - ============================================================
2025-08-04 15:34:32,521 - INFO - [步骤 1/5] 为 '南京警方' 启动 Cookie 抓取器...
2025-08-04 15:34:32,521 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:34:32,521 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:34:32,522 - INFO - === 开始重置网络状态 ===
2025-08-04 15:34:32,522 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:34:32,606 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:34:32,606 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:34:32,606 - INFO - 系统代理已成功关闭
2025-08-04 15:34:32,607 - INFO - ✅ 代理关闭操作
2025-08-04 15:34:32,607 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:34:33,835 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:33,836 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:34:33,837 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:34:33,837 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:34:33,838 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:34:34,337 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:34:34,338 - INFO - 🔄 Cookie抓取器进程已启动，PID: 36784
2025-08-04 15:34:34,338 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:34:37,339 - INFO - 等待代理服务启动...
2025-08-04 15:34:38,156 - INFO - 代理服务已启动并正常工作
2025-08-04 15:34:38,156 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 36784)
2025-08-04 15:34:38,156 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:34:38,157 - INFO - [步骤 2/5] 为 '南京警方' 启动 UI 自动化...
2025-08-04 15:34:38,157 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:34:38,158 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:34:38,158 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:34:38,158 - INFO - 正在查找微信主窗口...
2025-08-04 15:34:49,102 - WARNING - 未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...
2025-08-04 15:34:54,149 - ERROR - 未找到微信主窗口，请确保微信已登录并显示主界面。
2025-08-04 15:34:54,149 - ERROR - 发送链接失败，流程中止。
2025-08-04 15:34:54,149 - ERROR - ❌ 公众号 '南京警方' UI 自动化触发失败，跳过此公众号
2025-08-04 15:34:54,149 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:34:54,149 - INFO - 正在停止Cookie抓取器 (PID: 36784)...
2025-08-04 15:34:54,150 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:34:54,150 - INFO - 正在验证并清理代理设置...
2025-08-04 15:34:54,150 - INFO - === 开始重置网络状态 ===
2025-08-04 15:34:54,150 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:34:54,226 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:34:54,227 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:34:54,227 - INFO - 系统代理已成功关闭
2025-08-04 15:34:54,227 - INFO - ✅ 代理关闭操作
2025-08-04 15:34:54,227 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:34:55,655 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:55,656 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:34:55,656 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:34:56,907 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:34:56,908 - INFO - ✅ 网络连接验证正常
2025-08-04 15:34:56,909 - INFO - ================================================================================
2025-08-04 15:34:56,909 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 15:34:56,909 - INFO - ================================================================================
2025-08-04 15:34:56,910 - INFO - ✅ 成功处理: 0 个公众号
2025-08-04 15:34:56,910 - INFO - ❌ 失败处理: 2 个公众号
2025-08-04 15:34:56,912 - INFO - 📄 总计文章: 0 篇
2025-08-04 15:34:56,912 - INFO - ================================================================================
2025-08-04 15:34:56,913 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 15:34:56,913 - INFO - ================================================================================
2025-08-04 15:35:14,227 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:35:14,228 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:35:14,233 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:35:14,233 - INFO - ================================================================================
2025-08-04 15:35:14,233 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:35:14,233 - INFO - ================================================================================
2025-08-04 15:35:14,233 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:35:14,305 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:35:14,306 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:35:14,306 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:35:14,307 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:35:14,307 - INFO - ============================================================
2025-08-04 15:35:14,307 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:35:14,307 - INFO - ============================================================
2025-08-04 15:35:14,307 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:35:14,307 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:35:14,307 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:35:14,307 - INFO - === 开始重置网络状态 ===
2025-08-04 15:35:14,307 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:35:14,393 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:35:14,394 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:35:14,394 - INFO - 系统代理已成功关闭
2025-08-04 15:35:14,394 - INFO - ✅ 代理关闭操作
2025-08-04 15:35:14,394 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:35:15,330 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:35:15,330 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:35:15,330 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:35:15,330 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:35:15,330 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:35:15,828 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:35:15,829 - INFO - 🔄 Cookie抓取器进程已启动，PID: 30904
2025-08-04 15:35:15,829 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:35:18,829 - INFO - 等待代理服务启动...
2025-08-04 15:35:19,311 - INFO - 代理服务已启动并正常工作
2025-08-04 15:35:19,312 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 30904)
2025-08-04 15:35:19,312 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:35:19,312 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:35:19,313 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:35:19,313 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:35:19,313 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:35:19,314 - INFO - 正在查找微信主窗口...
2025-08-04 15:35:19,380 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:35:19,380 - INFO - 正在激活微信窗口...
2025-08-04 15:35:21,890 - INFO - 微信窗口已激活。
2025-08-04 15:35:21,891 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:35:28,601 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:35:28,602 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:35:31,934 - INFO - 正在查找聊天输入框...
2025-08-04 15:35:33,935 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:35:33,947 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:35:33,948 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:35:35,526 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:35:37,814 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:35:37,990 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:35:38,800 - INFO - 链接已发送。
2025-08-04 15:35:41,801 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:35:43,893 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:35:44,618 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:35:47,619 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:35:47,619 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:35:47,620 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:35:47,633 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:35:50,340 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:35:51,841 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:35:59,969 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:35:59,969 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:35:59,969 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:35:59,969 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:35:59,969 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:35:59,973 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:02,681 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:03,182 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:03,183 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:03,192 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:05,900 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:07,401 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:16,059 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:16,059 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:18,560 - INFO - 第 1 次刷新完成
2025-08-04 15:36:20,061 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:36:20,062 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:20,062 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:20,070 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:22,775 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:23,275 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:23,276 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:23,283 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:25,990 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:27,491 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:36,139 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:36,139 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:38,640 - INFO - 第 2 次刷新完成
2025-08-04 15:36:40,141 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:36:40,142 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:40,143 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:40,154 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:42,859 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:43,360 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:36:43,360 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:36:43,365 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:36:46,073 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:36:47,573 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:36:56,240 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:36:56,241 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:36:58,741 - INFO - 第 3 次刷新完成
2025-08-04 15:36:58,742 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:36:58,744 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:36:58,744 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:36:58,745 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:36:59,746 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:36:59,748 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:36:59,749 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:36:59,750 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:36:59,751 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:36:59,751 - INFO - 正在停止Cookie抓取器 (PID: 30904)...
2025-08-04 15:36:59,753 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:36:59,753 - INFO - 正在验证并清理代理设置...
2025-08-04 15:36:59,753 - INFO - === 开始重置网络状态 ===
2025-08-04 15:36:59,753 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:36:59,885 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:36:59,885 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:36:59,885 - INFO - 系统代理已成功关闭
2025-08-04 15:36:59,885 - INFO - ✅ 代理关闭操作
2025-08-04 15:36:59,885 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:37:01,549 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:37:01,550 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:37:01,550 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:37:03,494 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:37:03,495 - INFO - ✅ 网络连接验证正常
2025-08-04 15:37:06,496 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:37:06,497 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-04 15:39:18,249 - INFO - ✅ 公众号 '南京党建' 爬取完成！获取 15 篇文章
2025-08-04 15:39:18,250 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京党建_20250804_153918.xlsx
2025-08-04 15:39:18,250 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 15:39:33,250 - INFO - ============================================================
2025-08-04 15:39:33,251 - INFO - 📍 处理第 2/2 个公众号: 南京警方
2025-08-04 15:39:33,251 - INFO - ============================================================
2025-08-04 15:39:33,251 - INFO - [步骤 1/5] 为 '南京警方' 启动 Cookie 抓取器...
2025-08-04 15:39:33,252 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:39:33,252 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:39:33,252 - INFO - === 开始重置网络状态 ===
2025-08-04 15:39:33,253 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:39:33,348 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:39:33,349 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:39:33,349 - INFO - 系统代理已成功关闭
2025-08-04 15:39:33,349 - INFO - ✅ 代理关闭操作
2025-08-04 15:39:33,349 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:39:35,589 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:39:35,589 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:39:35,590 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:39:35,590 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:39:35,591 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:39:36,174 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:39:36,175 - INFO - 🔄 Cookie抓取器进程已启动，PID: 7264
2025-08-04 15:39:36,175 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:39:39,176 - INFO - 等待代理服务启动...
2025-08-04 15:39:40,436 - INFO - 代理服务已启动并正常工作
2025-08-04 15:39:40,437 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 7264)
2025-08-04 15:39:40,437 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:39:40,437 - INFO - [步骤 2/5] 为 '南京警方' 启动 UI 自动化...
2025-08-04 15:39:40,438 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:39:40,438 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:39:40,439 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:39:40,439 - INFO - 正在查找微信主窗口...
2025-08-04 15:39:40,604 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:39:40,604 - INFO - 正在激活微信窗口...
2025-08-04 15:39:43,118 - INFO - 微信窗口已激活。
2025-08-04 15:39:43,119 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:39:49,802 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:39:49,803 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:39:53,136 - INFO - 正在查找聊天输入框...
2025-08-04 15:39:55,137 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:39:55,145 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:39:55,145 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:39:56,711 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&mid=2247869690&idx=1&sn=11191456c1a9d5d930f1a385c2871b68&chksm=eaa0d2a4ddd75bb2309e8c8eac541bfbc8a2a4519ad5bd98fde6ad96e532cf17dc9fc7cb72a5#rd
2025-08-04 15:39:58,999 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:39:59,198 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:40:00,001 - INFO - 链接已发送。
2025-08-04 15:40:03,002 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:40:05,094 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:40:05,817 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:40:08,818 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:40:08,819 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:08,819 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:08,822 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:11,527 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:13,028 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:21,294 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:40:21,294 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:40:21,294 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:40:21,294 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:21,294 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:21,299 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:24,005 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:24,506 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:24,506 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:24,517 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:27,223 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:28,724 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:37,498 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:40:37,499 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:40:40,000 - INFO - 第 1 次刷新完成
2025-08-04 15:40:41,501 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:40:41,501 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:41,501 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:41,506 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:44,211 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:44,712 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:40:44,713 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:40:44,725 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:40:47,431 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:40:48,933 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:40:57,653 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:40:57,654 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:41:00,154 - INFO - 第 2 次刷新完成
2025-08-04 15:41:01,655 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:41:01,656 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:41:01,656 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:41:01,665 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:41:04,371 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:41:04,872 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:41:04,873 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:41:04,881 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:41:07,587 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:41:09,088 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:41:17,833 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:41:17,834 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:41:20,334 - INFO - 第 3 次刷新完成
2025-08-04 15:41:20,335 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:41:20,336 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:41:20,337 - INFO - [步骤 3/5] 等待 '南京警方' 的 Cookie 数据...
2025-08-04 15:41:20,337 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:41:21,338 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:41:21,339 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:41:21,340 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:41:21,340 - INFO - [步骤 4/5] 停止 '南京警方' 的 Cookie 抓取器...
2025-08-04 15:41:21,341 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:41:21,342 - INFO - 正在停止Cookie抓取器 (PID: 7264)...
2025-08-04 15:41:21,345 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:41:21,345 - INFO - 正在验证并清理代理设置...
2025-08-04 15:41:21,345 - INFO - === 开始重置网络状态 ===
2025-08-04 15:41:21,346 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:41:21,460 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:41:21,460 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:41:21,461 - INFO - 系统代理已成功关闭
2025-08-04 15:41:21,461 - INFO - ✅ 代理关闭操作
2025-08-04 15:41:21,461 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:41:24,050 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:41:24,051 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:41:24,051 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:41:26,042 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:41:26,042 - INFO - ✅ 网络连接验证正常
2025-08-04 15:41:29,043 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:41:29,044 - INFO - [步骤 5/5] 开始爬取 '南京警方' 的文章...
2025-08-04 15:41:29,166 - WARNING - ⚠️ 公众号 '南京警方' 未获取到任何文章数据
2025-08-04 15:41:29,166 - INFO - ================================================================================
2025-08-04 15:41:29,166 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 15:41:29,167 - INFO - ================================================================================
2025-08-04 15:41:29,167 - INFO - ✅ 成功处理: 1 个公众号
2025-08-04 15:41:29,167 - INFO - ❌ 失败处理: 1 个公众号
2025-08-04 15:41:29,167 - INFO - 📄 总计文章: 15 篇
2025-08-04 15:41:29,201 - INFO - 🎉 汇总数据已保存到:
2025-08-04 15:41:29,202 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_154129.xlsx
2025-08-04 15:41:29,202 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_154129.json
2025-08-04 15:41:29,202 - INFO - ================================================================================
2025-08-04 15:41:29,202 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 15:41:29,202 - INFO - ================================================================================
2025-08-04 15:46:54,373 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 15:46:54,374 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 15:46:54,379 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 15:46:54,379 - INFO - ================================================================================
2025-08-04 15:46:54,379 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 15:46:54,379 - INFO - ================================================================================
2025-08-04 15:46:54,380 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 15:46:54,449 - INFO - 找到有效目标 1: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 15:46:54,450 - INFO - 找到有效目标 2: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 15:46:54,451 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 15:46:54,451 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 15:46:54,451 - INFO - ============================================================
2025-08-04 15:46:54,451 - INFO - 📍 处理第 1/2 个公众号: 南京党建
2025-08-04 15:46:54,451 - INFO - ============================================================
2025-08-04 15:46:54,451 - INFO - [步骤 1/5] 为 '南京党建' 启动 Cookie 抓取器...
2025-08-04 15:46:54,451 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:46:54,451 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:46:54,451 - INFO - === 开始重置网络状态 ===
2025-08-04 15:46:54,451 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:46:54,535 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:46:54,535 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:46:54,536 - INFO - 系统代理已成功关闭
2025-08-04 15:46:54,536 - INFO - ✅ 代理关闭操作
2025-08-04 15:46:54,536 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:46:57,021 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:46:57,021 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:46:57,022 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:46:57,022 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:46:57,023 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:46:57,520 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:46:57,520 - INFO - 🔄 Cookie抓取器进程已启动，PID: 24116
2025-08-04 15:46:57,521 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:47:00,521 - INFO - 等待代理服务启动...
2025-08-04 15:47:01,505 - INFO - 代理服务已启动并正常工作
2025-08-04 15:47:01,506 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 24116)
2025-08-04 15:47:01,506 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:47:01,506 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-04 15:47:01,507 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:47:01,508 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:47:01,508 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:47:01,508 - INFO - 正在查找微信主窗口...
2025-08-04 15:47:02,438 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:47:02,438 - INFO - 正在激活微信窗口...
2025-08-04 15:47:04,956 - INFO - 微信窗口已激活。
2025-08-04 15:47:04,957 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:47:11,637 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:47:11,637 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:47:14,968 - INFO - 正在查找聊天输入框...
2025-08-04 15:47:16,969 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:47:16,978 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:47:16,979 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:47:18,561 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-04 15:47:20,849 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:47:21,040 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:47:21,850 - INFO - 链接已发送。
2025-08-04 15:47:24,851 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:47:26,941 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:47:27,665 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:47:30,666 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:47:30,667 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:47:30,668 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:47:30,675 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:47:33,382 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:47:34,883 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:47:43,173 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:47:43,174 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:47:43,174 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:47:43,174 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:47:43,174 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:47:43,177 - INFO - 尝试传统窗口查找方法...
2025-08-04 15:47:46,419 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 15:47:48,235 - INFO - 已成功激活浏览器窗口
2025-08-04 15:47:48,737 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:47:48,737 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:47:48,752 - INFO - 通过焦点检测找到浏览器窗口: '微信' (WeChatMainWndForPC)
2025-08-04 15:47:51,458 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:47:52,959 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:48:01,713 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:48:01,713 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:48:04,214 - INFO - 第 1 次刷新完成
2025-08-04 15:48:05,716 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:48:05,717 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:48:05,718 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:48:05,726 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:48:08,432 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:48:08,933 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:48:08,934 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:48:08,945 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:48:11,651 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:48:13,152 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:48:21,908 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:48:21,909 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:48:24,409 - INFO - 第 2 次刷新完成
2025-08-04 15:48:25,910 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:48:25,911 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:48:25,912 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:48:25,922 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:48:28,628 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:48:29,129 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:48:29,130 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:48:29,139 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:48:31,843 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:48:33,344 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:48:42,100 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:48:42,100 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:48:44,600 - INFO - 第 3 次刷新完成
2025-08-04 15:48:44,601 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:48:44,602 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:48:44,603 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-04 15:48:44,604 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:48:45,604 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:48:45,606 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:48:45,607 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:48:45,607 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-04 15:48:45,608 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:48:45,609 - INFO - 正在停止Cookie抓取器 (PID: 24116)...
2025-08-04 15:48:45,611 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:48:45,612 - INFO - 正在验证并清理代理设置...
2025-08-04 15:48:45,612 - INFO - === 开始重置网络状态 ===
2025-08-04 15:48:45,612 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:48:45,707 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:48:45,707 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:48:45,708 - INFO - 系统代理已成功关闭
2025-08-04 15:48:45,708 - INFO - ✅ 代理关闭操作
2025-08-04 15:48:45,708 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:48:46,914 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:48:46,914 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:48:46,915 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:48:48,214 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:48:48,215 - INFO - ✅ 网络连接验证正常
2025-08-04 15:48:51,216 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:48:51,217 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-04 15:51:06,266 - INFO - ✅ 公众号 '南京党建' 爬取完成！获取 15 篇文章
2025-08-04 15:51:06,266 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京党建_20250804_155106.xlsx
2025-08-04 15:51:06,266 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 15:51:21,267 - INFO - ============================================================
2025-08-04 15:51:21,267 - INFO - 📍 处理第 2/2 个公众号: 南京警方
2025-08-04 15:51:21,268 - INFO - ============================================================
2025-08-04 15:51:21,268 - INFO - [步骤 1/5] 为 '南京警方' 启动 Cookie 抓取器...
2025-08-04 15:51:21,269 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 15:51:21,269 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 15:51:21,270 - INFO - === 开始重置网络状态 ===
2025-08-04 15:51:21,270 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:51:21,366 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:51:21,367 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:51:21,367 - INFO - 系统代理已成功关闭
2025-08-04 15:51:21,367 - INFO - ✅ 代理关闭操作
2025-08-04 15:51:21,367 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:51:22,642 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:51:22,642 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:51:22,643 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 15:51:22,643 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 15:51:22,644 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 15:51:23,183 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 15:51:23,184 - INFO - 🔄 Cookie抓取器进程已启动，PID: 40656
2025-08-04 15:51:23,184 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 15:51:26,185 - INFO - 等待代理服务启动...
2025-08-04 15:51:26,639 - INFO - 代理服务已启动并正常工作
2025-08-04 15:51:26,639 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 40656)
2025-08-04 15:51:26,640 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 15:51:26,640 - INFO - [步骤 2/5] 为 '南京警方' 启动 UI 自动化...
2025-08-04 15:51:26,640 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 15:51:26,640 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 15:51:26,641 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 15:51:26,641 - INFO - 正在查找微信主窗口...
2025-08-04 15:51:26,682 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 15:51:26,683 - INFO - 正在激活微信窗口...
2025-08-04 15:51:29,191 - INFO - 微信窗口已激活。
2025-08-04 15:51:29,192 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 15:51:35,920 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 15:51:35,920 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 15:51:39,252 - INFO - 正在查找聊天输入框...
2025-08-04 15:51:41,252 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 15:51:41,262 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 15:51:41,263 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 15:51:42,826 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&mid=2247869690&idx=1&sn=11191456c1a9d5d930f1a385c2871b68&chksm=eaa0d2a4ddd75bb2309e8c8eac541bfbc8a2a4519ad5bd98fde6ad96e532cf17dc9fc7cb72a5#rd
2025-08-04 15:51:45,114 - INFO - 链接已粘贴，正在发送...
2025-08-04 15:51:45,310 - INFO - 找到发送按钮，点击发送...
2025-08-04 15:51:46,117 - INFO - 链接已发送。
2025-08-04 15:51:49,119 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 15:51:51,212 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 15:51:51,936 - INFO - ✅ 成功点击最新链接。
2025-08-04 15:51:54,937 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 15:51:54,937 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:51:54,937 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:51:54,941 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:51:57,646 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:51:59,147 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:52:07,404 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 15:52:07,404 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 15:52:07,404 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 15:52:07,404 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:07,404 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:07,407 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:10,112 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:10,613 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:10,614 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:10,624 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:13,330 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:14,832 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:52:23,609 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:52:23,610 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:52:26,111 - INFO - 第 1 次刷新完成
2025-08-04 15:52:27,611 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 15:52:27,612 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:27,612 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:27,620 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:30,326 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:30,827 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:30,828 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:30,839 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:33,544 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:35,045 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:52:43,804 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:52:43,805 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:52:46,306 - INFO - 第 2 次刷新完成
2025-08-04 15:52:47,807 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 15:52:47,808 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:47,808 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:47,820 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:50,525 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:51,026 - INFO - 正在查找微信浏览器窗口...
2025-08-04 15:52:51,027 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 15:52:51,041 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 15:52:53,747 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 15:52:55,248 - INFO - 正在检测SSL证书错误页面...
2025-08-04 15:53:04,003 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 15:53:04,004 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 15:53:06,504 - INFO - 第 3 次刷新完成
2025-08-04 15:53:06,505 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 15:53:06,506 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 15:53:06,507 - INFO - [步骤 3/5] 等待 '南京警方' 的 Cookie 数据...
2025-08-04 15:53:06,507 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 15:53:07,507 - INFO - 检测到Cookie文件已生成。
2025-08-04 15:53:07,509 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 15:53:07,510 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 15:53:07,510 - INFO - [步骤 4/5] 停止 '南京警方' 的 Cookie 抓取器...
2025-08-04 15:53:07,511 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 15:53:07,511 - INFO - 正在停止Cookie抓取器 (PID: 40656)...
2025-08-04 15:53:07,514 - INFO - Cookie抓取器已成功终止。
2025-08-04 15:53:07,514 - INFO - 正在验证并清理代理设置...
2025-08-04 15:53:07,514 - INFO - === 开始重置网络状态 ===
2025-08-04 15:53:07,515 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 15:53:07,627 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 15:53:07,627 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 15:53:07,627 - INFO - 系统代理已成功关闭
2025-08-04 15:53:07,627 - INFO - ✅ 代理关闭操作
2025-08-04 15:53:07,627 - INFO - 🔗 正在验证网络连接...
2025-08-04 15:53:08,863 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:53:08,863 - INFO - ✅ 网络状态重置验证完成
2025-08-04 15:53:08,864 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 15:53:11,026 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 15:53:11,027 - INFO - ✅ 网络连接验证正常
2025-08-04 15:53:14,027 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 15:53:14,028 - INFO - [步骤 5/5] 开始爬取 '南京警方' 的文章...
2025-08-04 15:55:38,809 - INFO - ✅ 公众号 '南京警方' 爬取完成！获取 14 篇文章
2025-08-04 15:55:38,809 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京警方_20250804_155538.xlsx
2025-08-04 15:55:38,809 - INFO - ================================================================================
2025-08-04 15:55:38,809 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 15:55:38,809 - INFO - ================================================================================
2025-08-04 15:55:38,809 - INFO - ✅ 成功处理: 2 个公众号
2025-08-04 15:55:38,809 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 15:55:38,809 - INFO - 📄 总计文章: 29 篇
2025-08-04 15:55:38,827 - INFO - 🎉 汇总数据已保存到:
2025-08-04 15:55:38,827 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_155538.xlsx
2025-08-04 15:55:38,828 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_155538.json
2025-08-04 15:55:38,828 - INFO - ================================================================================
2025-08-04 15:55:38,828 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 15:55:38,828 - INFO - ================================================================================
2025-08-04 16:05:19,496 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:05:19,496 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:05:19,503 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:05:19,503 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:05:19,503 - INFO - ================================================================================
2025-08-04 16:05:19,503 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:05:19,504 - INFO - ================================================================================
2025-08-04 16:05:19,504 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:05:19,577 - INFO - 找到有效目标 1: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-04 16:05:19,577 - INFO - 找到有效目标 2: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-04 16:05:19,578 - INFO - 找到有效目标 3: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-04 16:05:19,578 - INFO - 找到有效目标 4: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-04 16:05:19,578 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:05:19,578 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:05:19,579 - INFO - ============================================================
2025-08-04 16:05:19,579 - INFO - 📍 处理第 1/4 个公众号: 南京市城管局
2025-08-04 16:05:19,579 - INFO - ============================================================
2025-08-04 16:05:19,579 - INFO - [步骤 1/5] 为 '南京市城管局' 启动 Cookie 抓取器...
2025-08-04 16:05:19,579 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:05:19,579 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:05:19,579 - INFO - === 开始重置网络状态 ===
2025-08-04 16:05:19,579 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:05:19,661 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:05:19,661 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:05:19,661 - INFO - 系统代理已成功关闭
2025-08-04 16:05:19,661 - INFO - ✅ 代理关闭操作
2025-08-04 16:05:19,661 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:05:23,677 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:05:23,678 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:05:23,678 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:05:23,679 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:05:23,680 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:05:24,187 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:05:24,188 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32276
2025-08-04 16:05:24,188 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:05:27,189 - INFO - 等待代理服务启动...
2025-08-04 16:05:32,662 - INFO - 代理服务已启动并正常工作
2025-08-04 16:05:32,663 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32276)
2025-08-04 16:05:32,664 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:05:32,664 - INFO - [步骤 2/5] 为 '南京市城管局' 启动 UI 自动化...
2025-08-04 16:05:32,665 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:05:32,666 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:05:32,666 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:05:32,667 - INFO - 正在查找微信主窗口...
2025-08-04 16:05:33,461 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:05:33,462 - INFO - 正在激活微信窗口...
2025-08-04 16:05:35,974 - INFO - 微信窗口已激活。
2025-08-04 16:05:35,974 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:05:42,720 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:05:42,721 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:05:46,047 - INFO - 正在查找聊天输入框...
2025-08-04 16:05:48,048 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:05:48,061 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:05:48,061 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:05:49,655 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&mid=2247564011&idx=1&sn=e94d40f840abcb2aedaf3f93eafa8372&chksm=eb4b7732dc3cfe24e9d2c8c4075e9d8b23edd57f84c7e6ff8510365992e761a0d21e2988bc70#rd
2025-08-04 16:05:51,941 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:05:52,152 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:05:52,953 - INFO - 链接已发送。
2025-08-04 16:05:55,955 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:05:58,051 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:05:58,783 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:06:01,784 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:06:01,785 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:06:01,785 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:06:01,794 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:06:04,500 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:06:06,002 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:06:13,996 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:06:13,996 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:06:13,996 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:06:13,996 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:06:13,997 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:13,997 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:06:13,997 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:06:13,998 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:13,998 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:06:13,998 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:06:13,998 - INFO - [步骤 3/5] 等待 '南京市城管局' 的 Cookie 数据...
2025-08-04 16:06:13,999 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:06:14,999 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:06:15,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:15,001 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:06:15,001 - INFO - [步骤 4/5] 停止 '南京市城管局' 的 Cookie 抓取器...
2025-08-04 16:06:15,001 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:06:15,002 - INFO - 正在停止Cookie抓取器 (PID: 32276)...
2025-08-04 16:06:15,004 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:06:15,004 - INFO - 正在验证并清理代理设置...
2025-08-04 16:06:15,004 - INFO - === 开始重置网络状态 ===
2025-08-04 16:06:15,005 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:06:15,096 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:06:15,097 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:06:15,097 - INFO - 系统代理已成功关闭
2025-08-04 16:06:15,097 - INFO - ✅ 代理关闭操作
2025-08-04 16:06:15,097 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:06:16,587 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:16,587 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:06:16,587 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:06:18,804 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:18,805 - INFO - ✅ 网络连接验证正常
2025-08-04 16:06:21,806 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:06:21,807 - INFO - [步骤 5/5] 开始爬取 '南京市城管局' 的文章...
2025-08-04 16:06:21,938 - WARNING - ⚠️ 公众号 '南京市城管局' 未获取到任何文章数据
2025-08-04 16:06:21,939 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:06:36,939 - INFO - ============================================================
2025-08-04 16:06:36,940 - INFO - 📍 处理第 2/4 个公众号: 南京美丽乡村
2025-08-04 16:06:36,941 - INFO - ============================================================
2025-08-04 16:06:36,941 - INFO - [步骤 1/5] 为 '南京美丽乡村' 启动 Cookie 抓取器...
2025-08-04 16:06:36,942 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:06:36,942 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:06:36,943 - INFO - === 开始重置网络状态 ===
2025-08-04 16:06:36,943 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:06:37,039 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:06:37,039 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:06:37,040 - INFO - 系统代理已成功关闭
2025-08-04 16:06:37,040 - INFO - ✅ 代理关闭操作
2025-08-04 16:06:37,040 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:06:39,741 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:39,742 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:06:39,742 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:06:39,743 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:06:39,744 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:06:40,252 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:06:40,253 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21744
2025-08-04 16:06:40,253 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:06:43,254 - INFO - 等待代理服务启动...
2025-08-04 16:06:43,832 - INFO - 代理服务已启动并正常工作
2025-08-04 16:06:43,833 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21744)
2025-08-04 16:06:43,834 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:06:43,834 - INFO - [步骤 2/5] 为 '南京美丽乡村' 启动 UI 自动化...
2025-08-04 16:06:43,835 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:06:43,836 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:06:43,836 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:06:43,836 - INFO - 正在查找微信主窗口...
2025-08-04 16:06:44,090 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:06:44,090 - INFO - 正在激活微信窗口...
2025-08-04 16:06:46,608 - INFO - 微信窗口已激活。
2025-08-04 16:06:46,609 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:06:53,222 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:06:53,223 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:06:56,553 - INFO - 正在查找聊天输入框...
2025-08-04 16:06:58,554 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:06:58,565 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:06:58,565 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:07:00,150 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&mid=2247521900&idx=1&sn=7718b37c481a1948c6ff820192cf3207&chksm=97617896a016f180f83bc89bd8f8ae968e92b0b67273d0c0f71340d2a34bcb92436000b6e147#rd
2025-08-04 16:07:02,438 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:07:02,638 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:07:03,453 - INFO - 链接已发送。
2025-08-04 16:07:06,454 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:07:08,543 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:07:09,269 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:07:12,270 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:07:12,270 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:07:12,270 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:07:12,277 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:07:14,983 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:07:16,484 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:07:24,491 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:07:24,492 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:07:24,492 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:07:24,492 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:07:24,492 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:24,492 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:07:24,492 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:07:24,492 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:24,492 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:07:24,493 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:07:24,493 - INFO - [步骤 3/5] 等待 '南京美丽乡村' 的 Cookie 数据...
2025-08-04 16:07:24,493 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:07:25,493 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:07:25,495 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:25,495 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:07:25,495 - INFO - [步骤 4/5] 停止 '南京美丽乡村' 的 Cookie 抓取器...
2025-08-04 16:07:25,496 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:07:25,496 - INFO - 正在停止Cookie抓取器 (PID: 21744)...
2025-08-04 16:07:25,498 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:07:25,499 - INFO - 正在验证并清理代理设置...
2025-08-04 16:07:25,499 - INFO - === 开始重置网络状态 ===
2025-08-04 16:07:25,499 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:07:25,601 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:07:25,602 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:07:25,602 - INFO - 系统代理已成功关闭
2025-08-04 16:07:25,602 - INFO - ✅ 代理关闭操作
2025-08-04 16:07:25,603 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:07:27,192 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:07:27,192 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:07:27,192 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:07:28,338 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:07:28,339 - INFO - ✅ 网络连接验证正常
2025-08-04 16:07:31,339 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:07:31,340 - INFO - [步骤 5/5] 开始爬取 '南京美丽乡村' 的文章...
2025-08-04 16:07:31,462 - WARNING - ⚠️ 公众号 '南京美丽乡村' 未获取到任何文章数据
2025-08-04 16:07:31,463 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:09:09,522 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:09:09,522 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:09:44,387 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:10:02,751 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:10:02,752 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:10:02,757 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:10:02,757 - INFO - ================================================================================
2025-08-04 16:10:02,757 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:10:02,757 - INFO - ================================================================================
2025-08-04 16:10:02,757 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:10:02,829 - INFO - 找到有效目标 1: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 2: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 3: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 4: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-04 16:10:02,831 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:10:02,831 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:10:02,831 - INFO - ============================================================
2025-08-04 16:10:02,831 - INFO - 📍 处理第 1/4 个公众号: 南京市城管局
2025-08-04 16:10:02,832 - INFO - ============================================================
2025-08-04 16:10:02,832 - INFO - [步骤 1/5] 为 '南京市城管局' 启动 Cookie 抓取器...
2025-08-04 16:10:02,832 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:10:02,832 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:10:02,832 - INFO - === 开始重置网络状态 ===
2025-08-04 16:10:02,832 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:10:02,920 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:10:02,920 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:10:02,920 - INFO - 系统代理已成功关闭
2025-08-04 16:10:02,920 - INFO - ✅ 代理关闭操作
2025-08-04 16:10:02,920 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:10:05,382 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:05,383 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:10:05,383 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:10:05,383 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:10:05,383 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:10:05,886 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:10:05,887 - INFO - 🔄 Cookie抓取器进程已启动，PID: 25844
2025-08-04 16:10:05,887 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:10:08,887 - INFO - 等待代理服务启动...
2025-08-04 16:10:10,598 - INFO - 代理服务已启动并正常工作
2025-08-04 16:10:10,599 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 25844)
2025-08-04 16:10:10,599 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:10:10,600 - INFO - [步骤 2/5] 为 '南京市城管局' 启动 UI 自动化...
2025-08-04 16:10:10,600 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:10:10,601 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:10:10,601 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:10:10,601 - INFO - 正在查找微信主窗口...
2025-08-04 16:10:11,407 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:10:11,408 - INFO - 正在激活微信窗口...
2025-08-04 16:10:13,920 - INFO - 微信窗口已激活。
2025-08-04 16:10:13,921 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:10:20,613 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:10:20,614 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:10:23,952 - INFO - 正在查找聊天输入框...
2025-08-04 16:10:25,954 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:10:25,960 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:10:25,960 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:10:27,539 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&mid=2247564011&idx=1&sn=e94d40f840abcb2aedaf3f93eafa8372&chksm=eb4b7732dc3cfe24e9d2c8c4075e9d8b23edd57f84c7e6ff8510365992e761a0d21e2988bc70#rd
2025-08-04 16:10:29,828 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:10:30,035 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:10:30,835 - INFO - 链接已发送。
2025-08-04 16:10:33,835 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:10:35,942 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:10:36,670 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:10:39,671 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:10:39,672 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:10:39,672 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:10:39,683 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:10:42,390 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:10:43,891 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:10:51,891 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:10:51,891 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:10:51,891 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:10:51,891 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:10:51,892 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:51,892 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:10:51,892 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:10:51,892 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:51,892 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:10:51,892 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:10:51,892 - INFO - [步骤 3/5] 等待 '南京市城管局' 的 Cookie 数据...
2025-08-04 16:10:51,893 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:10:52,893 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:10:52,894 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:52,895 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:10:52,896 - INFO - [步骤 4/5] 停止 '南京市城管局' 的 Cookie 抓取器...
2025-08-04 16:10:52,896 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:10:52,897 - INFO - 正在停止Cookie抓取器 (PID: 25844)...
2025-08-04 16:10:52,901 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:10:52,901 - INFO - 正在验证并清理代理设置...
2025-08-04 16:10:52,902 - INFO - === 开始重置网络状态 ===
2025-08-04 16:10:52,902 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:10:52,993 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:10:52,993 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:10:52,993 - INFO - 系统代理已成功关闭
2025-08-04 16:10:52,993 - INFO - ✅ 代理关闭操作
2025-08-04 16:10:52,993 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:10:54,461 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:54,462 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:10:54,462 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:10:56,319 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:56,320 - INFO - ✅ 网络连接验证正常
2025-08-04 16:10:59,321 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:10:59,321 - INFO - [步骤 5/5] 开始爬取 '南京市城管局' 的文章...
2025-08-04 16:11:42,347 - INFO - ✅ 公众号 '南京市城管局' 爬取完成！获取 5 篇文章
2025-08-04 16:11:42,347 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京市城管局_20250804_161142.xlsx
2025-08-04 16:11:42,347 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:11:57,348 - INFO - ============================================================
2025-08-04 16:11:57,349 - INFO - 📍 处理第 2/4 个公众号: 南京美丽乡村
2025-08-04 16:11:57,350 - INFO - ============================================================
2025-08-04 16:11:57,350 - INFO - [步骤 1/5] 为 '南京美丽乡村' 启动 Cookie 抓取器...
2025-08-04 16:11:57,350 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:11:57,351 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:11:57,351 - INFO - === 开始重置网络状态 ===
2025-08-04 16:11:57,351 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:11:57,466 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:11:57,466 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:11:57,467 - INFO - 系统代理已成功关闭
2025-08-04 16:11:57,467 - INFO - ✅ 代理关闭操作
2025-08-04 16:11:57,467 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:11:59,129 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:11:59,129 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:11:59,129 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:11:59,129 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:11:59,130 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:11:59,672 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:11:59,673 - INFO - 🔄 Cookie抓取器进程已启动，PID: 7512
2025-08-04 16:11:59,673 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:12:02,673 - INFO - 等待代理服务启动...
2025-08-04 16:12:07,799 - INFO - 代理服务已启动并正常工作
2025-08-04 16:12:07,800 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 7512)
2025-08-04 16:12:07,800 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:12:07,801 - INFO - [步骤 2/5] 为 '南京美丽乡村' 启动 UI 自动化...
2025-08-04 16:12:07,801 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:12:07,802 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:12:07,802 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:12:07,803 - INFO - 正在查找微信主窗口...
2025-08-04 16:12:08,061 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:12:08,061 - INFO - 正在激活微信窗口...
2025-08-04 16:12:10,575 - INFO - 微信窗口已激活。
2025-08-04 16:12:10,576 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:12:17,242 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:12:17,243 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:12:20,570 - INFO - 正在查找聊天输入框...
2025-08-04 16:12:22,571 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:12:22,578 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:12:22,578 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:12:24,144 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&mid=2247521900&idx=1&sn=7718b37c481a1948c6ff820192cf3207&chksm=97617896a016f180f83bc89bd8f8ae968e92b0b67273d0c0f71340d2a34bcb92436000b6e147#rd
2025-08-04 16:12:26,431 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:12:26,649 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:12:27,335 - INFO - 链接已发送。
2025-08-04 16:12:30,337 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:12:32,426 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:12:33,152 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:12:36,154 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:12:36,154 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:12:36,155 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:12:36,166 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:12:38,872 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:12:40,373 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:12:48,386 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:12:48,386 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:12:48,386 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:12:48,386 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:12:48,387 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:48,387 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:12:48,387 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:12:48,387 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:48,387 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:12:48,387 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:12:48,387 - INFO - [步骤 3/5] 等待 '南京美丽乡村' 的 Cookie 数据...
2025-08-04 16:12:48,387 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:12:49,388 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:12:49,388 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:49,389 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:12:49,390 - INFO - [步骤 4/5] 停止 '南京美丽乡村' 的 Cookie 抓取器...
2025-08-04 16:12:49,390 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:12:49,390 - INFO - 正在停止Cookie抓取器 (PID: 7512)...
2025-08-04 16:12:49,393 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:12:49,393 - INFO - 正在验证并清理代理设置...
2025-08-04 16:12:49,394 - INFO - === 开始重置网络状态 ===
2025-08-04 16:12:49,395 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:12:49,498 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:12:49,499 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:12:49,499 - INFO - 系统代理已成功关闭
2025-08-04 16:12:49,499 - INFO - ✅ 代理关闭操作
2025-08-04 16:12:49,499 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:12:51,746 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:12:51,747 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:12:51,748 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:12:54,114 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:12:54,114 - INFO - ✅ 网络连接验证正常
2025-08-04 16:12:57,115 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:12:57,115 - INFO - [步骤 5/5] 开始爬取 '南京美丽乡村' 的文章...
2025-08-04 16:13:41,579 - INFO - ✅ 公众号 '南京美丽乡村' 爬取完成！获取 5 篇文章
2025-08-04 16:13:41,579 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京美丽乡村_20250804_161341.xlsx
2025-08-04 16:13:41,579 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:13:56,580 - INFO - ============================================================
2025-08-04 16:13:56,581 - INFO - 📍 处理第 3/4 个公众号: 南京应急管理
2025-08-04 16:13:56,582 - INFO - ============================================================
2025-08-04 16:13:56,583 - INFO - [步骤 1/5] 为 '南京应急管理' 启动 Cookie 抓取器...
2025-08-04 16:13:56,584 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:13:56,585 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:13:56,585 - INFO - === 开始重置网络状态 ===
2025-08-04 16:13:56,586 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:13:56,699 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:13:56,699 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:13:56,699 - INFO - 系统代理已成功关闭
2025-08-04 16:13:56,699 - INFO - ✅ 代理关闭操作
2025-08-04 16:13:56,699 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:13:57,893 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:13:57,894 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:13:57,894 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:13:57,895 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:13:57,895 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:13:58,425 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:13:58,426 - INFO - 🔄 Cookie抓取器进程已启动，PID: 27064
2025-08-04 16:13:58,426 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:14:01,426 - INFO - 等待代理服务启动...
2025-08-04 16:14:06,107 - INFO - 代理服务已启动并正常工作
2025-08-04 16:14:06,108 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 27064)
2025-08-04 16:14:06,108 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:14:06,108 - INFO - [步骤 2/5] 为 '南京应急管理' 启动 UI 自动化...
2025-08-04 16:14:06,108 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:14:06,109 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:14:06,109 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:14:06,109 - INFO - 正在查找微信主窗口...
2025-08-04 16:14:06,657 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:14:06,657 - INFO - 正在激活微信窗口...
2025-08-04 16:14:09,171 - INFO - 微信窗口已激活。
2025-08-04 16:14:09,172 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:14:15,671 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:14:15,671 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:14:19,002 - INFO - 正在查找聊天输入框...
2025-08-04 16:14:21,003 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:14:21,012 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:14:21,012 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:14:22,581 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&mid=2247688440&idx=1&sn=1b728e419d1fcbadc2c2e132bd18e976&chksm=e9b27d1ddec5f40b58ab6491fe0f56b49c4fce971a3a7f633241cf66c0317edfa5364d007f61#rd
2025-08-04 16:14:24,866 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:14:25,073 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:14:25,886 - INFO - 链接已发送。
2025-08-04 16:14:28,887 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:14:30,973 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:14:31,701 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:14:34,702 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:14:34,703 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:14:34,703 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:14:34,710 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:14:37,415 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:14:38,916 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:14:46,926 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:14:46,926 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:14:46,927 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:14:46,927 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:14:46,927 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:46,927 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:14:46,927 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:14:46,927 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:46,927 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:14:46,928 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:14:46,928 - INFO - [步骤 3/5] 等待 '南京应急管理' 的 Cookie 数据...
2025-08-04 16:14:46,928 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:14:47,929 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:14:47,930 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:47,931 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:14:47,931 - INFO - [步骤 4/5] 停止 '南京应急管理' 的 Cookie 抓取器...
2025-08-04 16:14:47,932 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:14:47,933 - INFO - 正在停止Cookie抓取器 (PID: 27064)...
2025-08-04 16:14:47,935 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:14:47,935 - INFO - 正在验证并清理代理设置...
2025-08-04 16:14:47,935 - INFO - === 开始重置网络状态 ===
2025-08-04 16:14:47,935 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:14:48,025 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:14:48,026 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:14:48,026 - INFO - 系统代理已成功关闭
2025-08-04 16:14:48,026 - INFO - ✅ 代理关闭操作
2025-08-04 16:14:48,026 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:14:48,999 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:14:49,000 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:14:49,001 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:14:51,170 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:14:51,171 - INFO - ✅ 网络连接验证正常
2025-08-04 16:14:54,171 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:14:54,172 - INFO - [步骤 5/5] 开始爬取 '南京应急管理' 的文章...
2025-08-04 16:14:54,277 - WARNING - ⚠️ 公众号 '南京应急管理' 未获取到任何文章数据
2025-08-04 16:14:54,277 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:24:43,003 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:24:43,003 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:24:43,008 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:33:28,241 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:33:28,241 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:33:28,248 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:33:28,248 - INFO - ================================================================================
2025-08-04 16:33:28,248 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:33:28,248 - INFO - ================================================================================
2025-08-04 16:33:28,249 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 16:33:28,330 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 16:33:28,331 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 16:33:28,331 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:33:28,331 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:33:28,331 - INFO - ============================================================
2025-08-04 16:33:28,331 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 16:33:28,331 - INFO - ============================================================
2025-08-04 16:33:28,331 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 16:33:28,331 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:33:28,332 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:33:28,332 - INFO - === 开始重置网络状态 ===
2025-08-04 16:33:28,332 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:33:28,421 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:33:28,422 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:33:28,422 - INFO - 系统代理已成功关闭
2025-08-04 16:33:28,422 - INFO - ✅ 代理关闭操作
2025-08-04 16:33:28,422 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:33:30,999 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:33:30,999 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:33:30,999 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:33:31,000 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:33:31,000 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:33:31,501 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:33:31,502 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23040
2025-08-04 16:33:31,502 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:33:34,503 - INFO - 等待代理服务启动...
2025-08-04 16:33:35,972 - INFO - 代理服务已启动并正常工作
2025-08-04 16:33:35,973 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23040)
2025-08-04 16:33:35,973 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:33:35,974 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 16:33:35,974 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:33:35,974 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:33:35,975 - INFO - 正在查找微信主窗口...
2025-08-04 16:33:36,058 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:33:36,058 - INFO - 正在激活微信窗口...
2025-08-04 16:33:38,574 - INFO - 微信窗口已激活。
2025-08-04 16:33:38,575 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:33:45,320 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:33:45,321 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:33:48,649 - INFO - 正在查找聊天输入框...
2025-08-04 16:33:50,650 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:33:50,665 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:33:50,666 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:33:52,265 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&mid=2247548170&idx=1&sn=56ac1f201d459218024a9d219d0a8099&chksm=96dd984ca1aa115a7469db8d4f401c5f3119965621cfd35de2e1adadb482f938f458f9b7896d#rd
2025-08-04 16:33:54,553 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:33:54,789 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:33:55,604 - INFO - 链接已发送。
2025-08-04 16:33:58,605 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:34:00,696 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:34:01,420 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:34:04,421 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:34:04,421 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:34:04,422 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:34:04,434 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:34:07,140 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:34:08,642 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:34:15,770 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:34:15,770 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:34:15,770 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:34:15,770 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:34:15,771 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:15,771 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:34:15,771 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:34:15,771 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:15,771 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:34:15,771 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:34:15,771 - INFO - [步骤 3/5] 等待 '法润栖霞' 的 Cookie 数据...
2025-08-04 16:34:15,771 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:34:16,772 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:34:16,773 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:34:16,773 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:34:16,773 - INFO - [步骤 4/5] 停止 '法润栖霞' 的 Cookie 抓取器...
2025-08-04 16:34:16,774 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:34:16,774 - INFO - 正在停止Cookie抓取器 (PID: 23040)...
2025-08-04 16:34:16,776 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:34:16,777 - INFO - 正在验证并清理代理设置...
2025-08-04 16:34:16,777 - INFO - === 开始重置网络状态 ===
2025-08-04 16:34:16,777 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:34:16,871 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:34:16,871 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:34:16,871 - INFO - 系统代理已成功关闭
2025-08-04 16:34:16,871 - INFO - ✅ 代理关闭操作
2025-08-04 16:34:16,871 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:34:18,800 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:18,801 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:34:18,801 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:34:20,575 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:20,575 - INFO - ✅ 网络连接验证正常
2025-08-04 16:34:23,576 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:34:23,577 - INFO - [步骤 5/5] 开始爬取 '法润栖霞' 的文章...
2025-08-04 16:34:23,696 - WARNING - ⚠️ 公众号 '法润栖霞' 未获取到任何文章数据
2025-08-04 16:34:23,697 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:34:38,697 - INFO - ============================================================
2025-08-04 16:34:38,698 - INFO - 📍 处理第 2/4 个公众号: 雨花司法
2025-08-04 16:34:38,699 - INFO - ============================================================
2025-08-04 16:34:38,700 - INFO - [步骤 1/5] 为 '雨花司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:34:38,701 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:34:38,701 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:34:38,702 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:34:38,702 - INFO - === 开始重置网络状态 ===
2025-08-04 16:34:38,702 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:34:38,786 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:34:38,786 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:34:38,787 - INFO - 系统代理已成功关闭
2025-08-04 16:34:38,787 - INFO - ✅ 代理关闭操作
2025-08-04 16:34:38,787 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:34:40,724 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:34:40,724 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:34:40,725 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:34:40,725 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:34:40,726 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:34:41,225 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:34:41,225 - INFO - 🔄 Cookie抓取器进程已启动，PID: 39848
2025-08-04 16:34:41,226 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:34:44,226 - INFO - 等待代理服务启动...
2025-08-04 16:34:44,715 - INFO - 代理服务已启动并正常工作
2025-08-04 16:34:44,716 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 39848)
2025-08-04 16:34:44,716 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:34:44,716 - INFO - [步骤 2/5] 为 '雨花司法' 启动 UI 自动化...
2025-08-04 16:34:44,717 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:34:44,717 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:34:44,718 - INFO - 正在查找微信主窗口...
2025-08-04 16:34:44,936 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:34:44,936 - INFO - 正在激活微信窗口...
2025-08-04 16:34:47,445 - INFO - 微信窗口已激活。
2025-08-04 16:34:47,446 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:34:54,123 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:34:54,123 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:34:57,450 - INFO - 正在查找聊天输入框...
2025-08-04 16:34:59,452 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:34:59,462 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:34:59,462 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:35:01,035 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&mid=2247544684&idx=2&sn=ee23948433c816bc0616572d322a1737&chksm=eba92461dcdead77bee6ed2a6f90ee7824edab22aab2d8c2df6e984090d4cd4eb5c2173b662d#rd
2025-08-04 16:35:03,324 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:35:03,541 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:35:04,355 - INFO - 链接已发送。
2025-08-04 16:35:07,356 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:35:09,441 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:35:10,171 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:35:13,172 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:35:13,173 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:35:13,173 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:35:13,180 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:35:15,885 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:35:17,386 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:35:24,707 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:35:24,707 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:35:24,707 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:35:24,707 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:35:24,707 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:24,707 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:35:24,707 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:35:24,708 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:24,708 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:35:24,708 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:35:24,708 - INFO - [步骤 3/5] 等待 '雨花司法' 的 Cookie 数据...
2025-08-04 16:35:24,708 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:35:25,708 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:35:25,710 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:35:25,710 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:35:25,711 - INFO - [步骤 4/5] 停止 '雨花司法' 的 Cookie 抓取器...
2025-08-04 16:35:25,711 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:35:25,712 - INFO - 正在停止Cookie抓取器 (PID: 39848)...
2025-08-04 16:35:25,714 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:35:25,715 - INFO - 正在验证并清理代理设置...
2025-08-04 16:35:25,716 - INFO - === 开始重置网络状态 ===
2025-08-04 16:35:25,716 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:35:25,807 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:35:25,808 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:35:25,808 - INFO - 系统代理已成功关闭
2025-08-04 16:35:25,808 - INFO - ✅ 代理关闭操作
2025-08-04 16:35:25,808 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:35:26,730 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:26,731 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:35:26,731 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:35:28,714 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:28,715 - INFO - ✅ 网络连接验证正常
2025-08-04 16:35:31,717 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:35:31,717 - INFO - [步骤 5/5] 开始爬取 '雨花司法' 的文章...
2025-08-04 16:35:31,842 - WARNING - ⚠️ 公众号 '雨花司法' 未获取到任何文章数据
2025-08-04 16:35:31,843 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:35:46,843 - INFO - ============================================================
2025-08-04 16:35:46,844 - INFO - 📍 处理第 3/4 个公众号: 江宁司法
2025-08-04 16:35:46,844 - INFO - ============================================================
2025-08-04 16:35:46,844 - INFO - [步骤 1/5] 为 '江宁司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:35:46,844 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:35:46,844 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:35:46,845 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:35:46,845 - INFO - === 开始重置网络状态 ===
2025-08-04 16:35:46,845 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:35:46,967 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:35:46,967 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:35:46,967 - INFO - 系统代理已成功关闭
2025-08-04 16:35:46,968 - INFO - ✅ 代理关闭操作
2025-08-04 16:35:46,968 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:35:48,275 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:35:48,276 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:35:48,276 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:35:48,276 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:35:48,277 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:35:48,775 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:35:48,776 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32896
2025-08-04 16:35:48,776 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:35:51,777 - INFO - 等待代理服务启动...
2025-08-04 16:35:52,641 - INFO - 代理服务已启动并正常工作
2025-08-04 16:35:52,641 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32896)
2025-08-04 16:35:52,642 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:35:52,642 - INFO - [步骤 2/5] 为 '江宁司法' 启动 UI 自动化...
2025-08-04 16:35:52,643 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:35:52,643 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:35:52,644 - INFO - 正在查找微信主窗口...
2025-08-04 16:35:53,131 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:35:53,131 - INFO - 正在激活微信窗口...
2025-08-04 16:35:55,643 - INFO - 微信窗口已激活。
2025-08-04 16:35:55,644 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:36:02,353 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:36:02,353 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:36:05,687 - INFO - 正在查找聊天输入框...
2025-08-04 16:36:07,689 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:36:07,699 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:36:07,699 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:36:09,289 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&mid=2652140303&idx=1&sn=74a10900f46e96be6567975b1d1ccd59&chksm=bda865f78adfece121715d85d10271b02fb19ce45ba1cfd5135e99209e0775ba3a82914ee28b#rd
2025-08-04 16:36:11,578 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:36:11,810 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:36:12,621 - INFO - 链接已发送。
2025-08-04 16:36:15,623 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:36:17,712 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:36:18,435 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:36:21,436 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:36:21,437 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:36:21,438 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:36:21,445 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:36:24,150 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:36:25,652 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:36:32,999 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:36:32,999 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:36:32,999 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:36:32,999 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:36:33,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:33,000 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:36:33,000 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:36:33,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:33,000 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:36:33,000 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:36:33,000 - INFO - [步骤 3/5] 等待 '江宁司法' 的 Cookie 数据...
2025-08-04 16:36:33,000 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:36:34,001 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:36:34,002 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:36:34,003 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:36:34,003 - INFO - [步骤 4/5] 停止 '江宁司法' 的 Cookie 抓取器...
2025-08-04 16:36:34,003 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:36:34,004 - INFO - 正在停止Cookie抓取器 (PID: 32896)...
2025-08-04 16:36:34,006 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:36:34,006 - INFO - 正在验证并清理代理设置...
2025-08-04 16:36:34,007 - INFO - === 开始重置网络状态 ===
2025-08-04 16:36:34,007 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:36:34,131 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:36:34,131 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:36:34,131 - INFO - 系统代理已成功关闭
2025-08-04 16:36:34,131 - INFO - ✅ 代理关闭操作
2025-08-04 16:36:34,131 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:36:35,700 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:35,700 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:36:35,701 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:36:36,620 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:36,621 - INFO - ✅ 网络连接验证正常
2025-08-04 16:36:39,622 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:36:39,623 - INFO - [步骤 5/5] 开始爬取 '江宁司法' 的文章...
2025-08-04 16:36:39,731 - WARNING - ⚠️ 公众号 '江宁司法' 未获取到任何文章数据
2025-08-04 16:36:39,732 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:36:54,732 - INFO - ============================================================
2025-08-04 16:36:54,733 - INFO - 📍 处理第 4/4 个公众号: 浦口普法
2025-08-04 16:36:54,734 - INFO - ============================================================
2025-08-04 16:36:54,736 - INFO - [步骤 1/5] 为 '浦口普法' 创建独立的 Cookie 抓取器...
2025-08-04 16:36:54,737 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:36:54,738 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:36:54,738 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:36:54,739 - INFO - === 开始重置网络状态 ===
2025-08-04 16:36:54,739 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:36:54,854 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:36:54,854 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:36:54,854 - INFO - 系统代理已成功关闭
2025-08-04 16:36:54,854 - INFO - ✅ 代理关闭操作
2025-08-04 16:36:54,855 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:36:57,354 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:36:57,354 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:36:57,354 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:36:57,355 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:36:57,355 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:36:57,849 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:36:57,850 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21332
2025-08-04 16:36:57,851 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:37:00,851 - INFO - 等待代理服务启动...
2025-08-04 16:37:06,311 - INFO - 代理服务已启动并正常工作
2025-08-04 16:37:06,311 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21332)
2025-08-04 16:37:06,312 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:37:06,312 - INFO - [步骤 2/5] 为 '浦口普法' 启动 UI 自动化...
2025-08-04 16:37:06,313 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:37:06,314 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:37:06,314 - INFO - 正在查找微信主窗口...
2025-08-04 16:37:06,548 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:37:06,548 - INFO - 正在激活微信窗口...
2025-08-04 16:37:09,064 - INFO - 微信窗口已激活。
2025-08-04 16:37:09,066 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:37:15,788 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:37:15,789 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:37:19,114 - INFO - 正在查找聊天输入框...
2025-08-04 16:37:21,116 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:37:21,128 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:37:21,129 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:37:22,692 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&mid=2452706749&idx=1&sn=b59ee1711a93713adc69fbbdc9b557c6&chksm=8cef28c6bb98a1d09cc437733de4ca183767c6e852a2fba8c0a86e699900e8cda2f1280fbac5#rd
2025-08-04 16:37:24,981 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:37:25,210 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:37:26,018 - INFO - 链接已发送。
2025-08-04 16:37:29,020 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:37:31,113 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:37:31,835 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:37:34,836 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:37:34,837 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:37:34,837 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:37:34,847 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:37:37,553 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:37:39,054 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:37:46,399 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:37:46,399 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:37:46,399 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:37:46,400 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:37:46,400 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:46,400 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:37:46,400 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:37:46,400 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:46,400 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:37:46,400 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:37:46,401 - INFO - [步骤 3/5] 等待 '浦口普法' 的 Cookie 数据...
2025-08-04 16:37:46,401 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:37:47,401 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:37:47,402 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:37:47,403 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:37:47,403 - INFO - [步骤 4/5] 停止 '浦口普法' 的 Cookie 抓取器...
2025-08-04 16:37:47,404 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:37:47,404 - INFO - 正在停止Cookie抓取器 (PID: 21332)...
2025-08-04 16:37:47,406 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:37:47,407 - INFO - 正在验证并清理代理设置...
2025-08-04 16:37:47,407 - INFO - === 开始重置网络状态 ===
2025-08-04 16:37:47,407 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:37:47,502 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:37:47,502 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:37:47,502 - INFO - 系统代理已成功关闭
2025-08-04 16:37:47,502 - INFO - ✅ 代理关闭操作
2025-08-04 16:37:47,502 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:37:48,439 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:37:48,440 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:37:48,440 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:37:49,390 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:37:49,390 - INFO - ✅ 网络连接验证正常
2025-08-04 16:37:52,391 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:37:52,392 - INFO - [步骤 5/5] 开始爬取 '浦口普法' 的文章...
2025-08-04 16:37:52,511 - WARNING - ⚠️ 公众号 '浦口普法' 未获取到任何文章数据
2025-08-04 16:37:52,511 - INFO - ================================================================================
2025-08-04 16:37:52,511 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 16:37:52,511 - INFO - ================================================================================
2025-08-04 16:37:52,512 - INFO - ✅ 成功处理: 0 个公众号
2025-08-04 16:37:52,512 - INFO - ❌ 失败处理: 4 个公众号
2025-08-04 16:37:52,512 - INFO - 📄 总计文章: 0 篇
2025-08-04 16:37:52,513 - INFO - ================================================================================
2025-08-04 16:37:52,513 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 16:37:52,514 - INFO - ================================================================================
2025-08-04 16:41:57,899 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 16:41:57,900 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 16:41:57,904 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:41:57,904 - INFO - ================================================================================
2025-08-04 16:41:57,905 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:41:57,905 - INFO - ================================================================================
2025-08-04 16:41:57,905 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:41:57,975 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 16:41:57,977 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:41:57,977 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:41:57,977 - INFO - ============================================================
2025-08-04 16:41:57,977 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 16:41:57,977 - INFO - ============================================================
2025-08-04 16:41:57,977 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 16:41:57,977 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:41:57,977 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:41:57,977 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:41:57,977 - INFO - === 开始重置网络状态 ===
2025-08-04 16:41:57,977 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:41:58,062 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:41:58,062 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:41:58,062 - INFO - 系统代理已成功关闭
2025-08-04 16:41:58,062 - INFO - ✅ 代理关闭操作
2025-08-04 16:41:58,062 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:41:59,645 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:41:59,646 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:41:59,647 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:41:59,647 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:41:59,649 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:42:00,148 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:42:00,149 - INFO - 🔄 Cookie抓取器进程已启动，PID: 27992
2025-08-04 16:42:00,149 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:42:03,150 - INFO - 等待代理服务启动...
2025-08-04 16:42:04,291 - INFO - 代理服务已启动并正常工作
2025-08-04 16:42:04,292 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 27992)
2025-08-04 16:42:04,292 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:42:04,292 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 16:42:04,292 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:42:04,293 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:42:04,293 - INFO - 正在查找微信主窗口...
2025-08-04 16:42:04,985 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:42:04,985 - INFO - 正在激活微信窗口...
2025-08-04 16:42:07,494 - INFO - 微信窗口已激活。
2025-08-04 16:42:07,495 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:42:14,186 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:42:14,187 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:42:17,520 - INFO - 正在查找聊天输入框...
2025-08-04 16:42:19,521 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:42:19,526 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:42:19,527 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:42:21,120 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&mid=2247548170&idx=1&sn=56ac1f201d459218024a9d219d0a8099&chksm=96dd984ca1aa115a7469db8d4f401c5f3119965621cfd35de2e1adadb482f938f458f9b7896d#rd
2025-08-04 16:42:23,410 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:42:23,646 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:42:24,454 - INFO - 链接已发送。
2025-08-04 16:42:27,455 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:42:29,546 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:42:30,285 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:42:33,286 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:42:33,286 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:42:33,287 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:42:33,292 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:42:36,000 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:42:37,501 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:42:44,847 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:42:44,848 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:42:44,848 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:42:44,849 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:42:44,849 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:44,849 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:42:44,850 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:42:44,850 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:44,850 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:42:44,850 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:42:44,850 - INFO - [步骤 3/5] 等待 '法润栖霞' 的 Cookie 数据...
2025-08-04 16:42:44,850 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:42:45,851 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:42:45,853 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:45,853 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:42:45,854 - INFO - [步骤 4/5] 停止 '法润栖霞' 的 Cookie 抓取器...
2025-08-04 16:42:45,854 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:42:45,855 - INFO - 正在停止Cookie抓取器 (PID: 27992)...
2025-08-04 16:42:45,856 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:42:45,856 - INFO - 正在验证并清理代理设置...
2025-08-04 16:42:45,857 - INFO - === 开始重置网络状态 ===
2025-08-04 16:42:45,857 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:42:45,952 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:42:45,952 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:42:45,952 - INFO - 系统代理已成功关闭
2025-08-04 16:42:45,952 - INFO - ✅ 代理关闭操作
2025-08-04 16:42:45,952 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:42:49,569 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:42:49,570 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:42:49,570 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:42:51,234 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:42:51,234 - INFO - ✅ 网络连接验证正常
2025-08-04 16:42:54,236 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:42:54,236 - INFO - [步骤 5/5] 开始爬取 '法润栖霞' 的文章...
2025-08-04 16:43:14,810 - INFO - ✅ 公众号 '法润栖霞' 爬取完成！获取 2 篇文章
2025-08-04 16:43:14,810 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_法润栖霞_20250804_164314.xlsx
2025-08-04 16:43:14,810 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:43:29,811 - INFO - ============================================================
2025-08-04 16:43:29,812 - INFO - 📍 处理第 2/4 个公众号: 雨花司法
2025-08-04 16:43:29,812 - INFO - ============================================================
2025-08-04 16:43:29,813 - INFO - [步骤 1/5] 为 '雨花司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:43:29,814 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:43:29,814 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:43:29,815 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:43:29,816 - INFO - === 开始重置网络状态 ===
2025-08-04 16:43:29,817 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:43:29,922 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:43:29,922 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:43:29,922 - INFO - 系统代理已成功关闭
2025-08-04 16:43:29,923 - INFO - ✅ 代理关闭操作
2025-08-04 16:43:29,923 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:43:31,137 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:43:31,138 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:43:31,138 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:43:31,138 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:43:31,139 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:43:31,645 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:43:31,646 - INFO - 🔄 Cookie抓取器进程已启动，PID: 18596
2025-08-04 16:43:31,646 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:43:34,646 - INFO - 等待代理服务启动...
2025-08-04 16:43:35,507 - INFO - 代理服务已启动并正常工作
2025-08-04 16:43:35,507 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 18596)
2025-08-04 16:43:35,508 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:43:35,508 - INFO - [步骤 2/5] 为 '雨花司法' 启动 UI 自动化...
2025-08-04 16:43:35,509 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:43:35,509 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:43:35,509 - INFO - 正在查找微信主窗口...
2025-08-04 16:43:35,744 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:43:35,744 - INFO - 正在激活微信窗口...
2025-08-04 16:43:38,262 - INFO - 微信窗口已激活。
2025-08-04 16:43:38,263 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:43:45,089 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:43:45,089 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:43:48,420 - INFO - 正在查找聊天输入框...
2025-08-04 16:43:50,421 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:43:50,428 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:43:50,428 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:43:52,005 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&mid=2247544684&idx=2&sn=ee23948433c816bc0616572d322a1737&chksm=eba92461dcdead77bee6ed2a6f90ee7824edab22aab2d8c2df6e984090d4cd4eb5c2173b662d#rd
2025-08-04 16:43:54,295 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:43:54,538 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:43:55,354 - INFO - 链接已发送。
2025-08-04 16:43:58,355 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:44:00,451 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:44:01,185 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:44:04,186 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:44:04,186 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:44:04,187 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:44:04,197 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:44:06,903 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:44:08,405 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:44:15,756 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:44:15,756 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:44:15,756 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:44:15,757 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:44:15,757 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:15,757 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:44:15,757 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:44:15,757 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:15,757 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:44:15,757 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:44:15,757 - INFO - [步骤 3/5] 等待 '雨花司法' 的 Cookie 数据...
2025-08-04 16:44:15,758 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:44:16,758 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:44:16,759 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:16,759 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:44:16,760 - INFO - [步骤 4/5] 停止 '雨花司法' 的 Cookie 抓取器...
2025-08-04 16:44:16,760 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:44:16,760 - INFO - 正在停止Cookie抓取器 (PID: 18596)...
2025-08-04 16:44:16,762 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:44:16,763 - INFO - 正在验证并清理代理设置...
2025-08-04 16:44:16,763 - INFO - === 开始重置网络状态 ===
2025-08-04 16:44:16,763 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:44:16,868 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:44:16,868 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:44:16,869 - INFO - 系统代理已成功关闭
2025-08-04 16:44:16,869 - INFO - ✅ 代理关闭操作
2025-08-04 16:44:16,869 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:44:17,837 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:44:17,837 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:44:17,838 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:44:18,774 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:44:18,774 - INFO - ✅ 网络连接验证正常
2025-08-04 16:44:21,775 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:44:21,776 - INFO - [步骤 5/5] 开始爬取 '雨花司法' 的文章...
2025-08-04 16:45:54,339 - INFO - ✅ 公众号 '雨花司法' 爬取完成！获取 9 篇文章
2025-08-04 16:45:54,339 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_雨花司法_20250804_164554.xlsx
2025-08-04 16:45:54,340 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:46:09,340 - INFO - ============================================================
2025-08-04 16:46:09,340 - INFO - 📍 处理第 3/4 个公众号: 江宁司法
2025-08-04 16:46:09,341 - INFO - ============================================================
2025-08-04 16:46:09,341 - INFO - [步骤 1/5] 为 '江宁司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:46:09,342 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:46:09,343 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:46:09,343 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:46:09,343 - INFO - === 开始重置网络状态 ===
2025-08-04 16:46:09,343 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:46:09,444 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:46:09,444 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:46:09,444 - INFO - 系统代理已成功关闭
2025-08-04 16:46:09,444 - INFO - ✅ 代理关闭操作
2025-08-04 16:46:09,444 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:46:12,487 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:46:12,487 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:46:12,488 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:46:12,488 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:46:12,489 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:46:12,984 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:46:12,985 - INFO - 🔄 Cookie抓取器进程已启动，PID: 15884
2025-08-04 16:46:12,985 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:46:15,986 - INFO - 等待代理服务启动...
2025-08-04 16:46:17,467 - INFO - 代理服务已启动并正常工作
2025-08-04 16:46:17,468 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 15884)
2025-08-04 16:46:17,469 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:46:17,469 - INFO - [步骤 2/5] 为 '江宁司法' 启动 UI 自动化...
2025-08-04 16:46:17,470 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:46:17,470 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:46:17,471 - INFO - 正在查找微信主窗口...
2025-08-04 16:46:17,943 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:46:17,944 - INFO - 正在激活微信窗口...
2025-08-04 16:46:20,458 - INFO - 微信窗口已激活。
2025-08-04 16:46:20,459 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:46:27,151 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:46:27,152 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:46:30,478 - INFO - 正在查找聊天输入框...
2025-08-04 16:46:32,478 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:46:32,488 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:46:32,489 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:46:34,058 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&mid=2652140303&idx=1&sn=74a10900f46e96be6567975b1d1ccd59&chksm=bda865f78adfece121715d85d10271b02fb19ce45ba1cfd5135e99209e0775ba3a82914ee28b#rd
2025-08-04 16:46:36,347 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:46:36,590 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:46:37,404 - INFO - 链接已发送。
2025-08-04 16:46:40,405 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:46:42,497 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:46:43,221 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:46:46,222 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:46:46,222 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:46:46,222 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:46:46,228 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:46:48,936 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:46:50,437 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:46:57,791 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:46:57,791 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:46:57,791 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:46:57,791 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:46:57,792 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:57,792 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:46:57,792 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:46:57,792 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:57,792 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:46:57,792 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:46:57,792 - INFO - [步骤 3/5] 等待 '江宁司法' 的 Cookie 数据...
2025-08-04 16:46:57,792 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:46:58,793 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:46:58,794 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:58,794 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:46:58,795 - INFO - [步骤 4/5] 停止 '江宁司法' 的 Cookie 抓取器...
2025-08-04 16:46:58,795 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:46:58,795 - INFO - 正在停止Cookie抓取器 (PID: 15884)...
2025-08-04 16:46:58,798 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:46:58,798 - INFO - 正在验证并清理代理设置...
2025-08-04 16:46:58,799 - INFO - === 开始重置网络状态 ===
2025-08-04 16:46:58,799 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:46:58,907 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:46:58,907 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:46:58,907 - INFO - 系统代理已成功关闭
2025-08-04 16:46:58,907 - INFO - ✅ 代理关闭操作
2025-08-04 16:46:58,907 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:47:01,689 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:47:01,689 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:47:01,690 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:47:02,612 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:47:02,613 - INFO - ✅ 网络连接验证正常
2025-08-04 16:47:05,614 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:47:05,615 - INFO - [步骤 5/5] 开始爬取 '江宁司法' 的文章...
2025-08-04 16:47:49,862 - INFO - ✅ 公众号 '江宁司法' 爬取完成！获取 5 篇文章
2025-08-04 16:47:49,862 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_江宁司法_20250804_164749.xlsx
2025-08-04 16:47:49,862 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:48:04,862 - INFO - ============================================================
2025-08-04 16:48:04,863 - INFO - 📍 处理第 4/4 个公众号: 浦口普法
2025-08-04 16:48:04,864 - INFO - ============================================================
2025-08-04 16:48:04,865 - INFO - [步骤 1/5] 为 '浦口普法' 创建独立的 Cookie 抓取器...
2025-08-04 16:48:04,866 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:48:04,867 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:48:04,868 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:48:04,868 - INFO - === 开始重置网络状态 ===
2025-08-04 16:48:04,869 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:48:04,975 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:48:04,976 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:48:04,976 - INFO - 系统代理已成功关闭
2025-08-04 16:48:04,976 - INFO - ✅ 代理关闭操作
2025-08-04 16:48:04,976 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:48:06,856 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:06,856 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:48:06,857 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:48:06,857 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:48:06,858 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:48:07,356 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:48:07,357 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33152
2025-08-04 16:48:07,357 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:48:10,357 - INFO - 等待代理服务启动...
2025-08-04 16:48:12,002 - INFO - 代理服务已启动并正常工作
2025-08-04 16:48:12,003 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33152)
2025-08-04 16:48:12,003 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:48:12,003 - INFO - [步骤 2/5] 为 '浦口普法' 启动 UI 自动化...
2025-08-04 16:48:12,003 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:48:12,003 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:48:12,003 - INFO - 正在查找微信主窗口...
2025-08-04 16:48:12,224 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:48:12,224 - INFO - 正在激活微信窗口...
2025-08-04 16:48:14,741 - INFO - 微信窗口已激活。
2025-08-04 16:48:14,741 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:48:21,436 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:48:21,437 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:48:24,771 - INFO - 正在查找聊天输入框...
2025-08-04 16:48:26,772 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:48:26,780 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:48:26,781 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:48:28,347 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&mid=2452706749&idx=1&sn=b59ee1711a93713adc69fbbdc9b557c6&chksm=8cef28c6bb98a1d09cc437733de4ca183767c6e852a2fba8c0a86e699900e8cda2f1280fbac5#rd
2025-08-04 16:48:30,639 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:48:30,885 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:48:31,684 - INFO - 链接已发送。
2025-08-04 16:48:34,686 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:48:36,777 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:48:37,504 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:48:40,505 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:48:40,506 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:48:40,506 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:48:40,514 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:48:43,220 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:48:44,720 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:48:52,090 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:48:52,091 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:48:52,091 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:48:52,091 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:48:52,091 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:52,091 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:48:52,091 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:48:52,091 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:52,092 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:48:52,092 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:48:52,092 - INFO - [步骤 3/5] 等待 '浦口普法' 的 Cookie 数据...
2025-08-04 16:48:52,092 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:48:53,092 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:48:53,094 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:53,094 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:48:53,094 - INFO - [步骤 4/5] 停止 '浦口普法' 的 Cookie 抓取器...
2025-08-04 16:48:53,095 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:48:53,095 - INFO - 正在停止Cookie抓取器 (PID: 33152)...
2025-08-04 16:48:53,099 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:48:53,100 - INFO - 正在验证并清理代理设置...
2025-08-04 16:48:53,100 - INFO - === 开始重置网络状态 ===
2025-08-04 16:48:53,100 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:48:53,198 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:48:53,198 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:48:53,198 - INFO - 系统代理已成功关闭
2025-08-04 16:48:53,198 - INFO - ✅ 代理关闭操作
2025-08-04 16:48:53,198 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:48:54,116 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:54,116 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:48:54,117 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:48:57,200 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:57,201 - INFO - ✅ 网络连接验证正常
2025-08-04 16:49:00,202 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:49:00,203 - INFO - [步骤 5/5] 开始爬取 '浦口普法' 的文章...
2025-08-04 16:49:22,100 - INFO - ✅ 公众号 '浦口普法' 爬取完成！获取 2 篇文章
2025-08-04 16:49:22,100 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_浦口普法_20250804_164922.xlsx
2025-08-04 16:49:22,100 - INFO - ================================================================================
2025-08-04 16:49:22,100 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 16:49:22,100 - INFO - ================================================================================
2025-08-04 16:49:22,100 - INFO - ✅ 成功处理: 4 个公众号
2025-08-04 16:49:22,100 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 16:49:22,100 - INFO - 📄 总计文章: 18 篇
2025-08-04 16:49:22,128 - INFO - 🎉 汇总数据已保存到:
2025-08-04 16:49:22,129 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_164922.xlsx
2025-08-04 16:49:22,129 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_164922.json
2025-08-04 16:49:22,129 - INFO - ================================================================================
2025-08-04 16:49:22,129 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 16:49:22,129 - INFO - ================================================================================
2025-08-04 17:00:35,763 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:00:35,764 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:00:35,770 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:00:35,771 - INFO - ================================================================================
2025-08-04 17:00:35,771 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:00:35,771 - INFO - ================================================================================
2025-08-04 17:00:35,771 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:00:35,844 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 17:00:35,846 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 17:00:35,846 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 17:00:35,847 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 17:00:35,847 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:00:35,847 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:00:35,847 - INFO - ============================================================
2025-08-04 17:00:35,848 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 17:00:35,848 - INFO - ============================================================
2025-08-04 17:00:35,848 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 17:00:35,848 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:00:35,848 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:00:35,848 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:00:35,849 - INFO - === 开始重置网络状态 ===
2025-08-04 17:00:35,849 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:00:35,943 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:00:35,943 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:00:35,943 - INFO - 系统代理已成功关闭
2025-08-04 17:00:35,943 - INFO - ✅ 代理关闭操作
2025-08-04 17:00:35,943 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:00:37,615 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:00:37,616 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:00:37,617 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:00:37,618 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:00:37,619 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:00:38,141 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:00:38,142 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21820
2025-08-04 17:00:38,142 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:00:41,142 - INFO - 等待代理服务启动...
2025-08-04 17:00:46,550 - INFO - 代理服务已启动并正常工作
2025-08-04 17:00:46,551 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21820)
2025-08-04 17:00:46,552 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:00:46,552 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 17:00:46,553 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:00:46,553 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:00:46,553 - INFO - 正在查找微信主窗口...
2025-08-04 17:00:48,436 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:00:48,437 - INFO - 正在激活微信窗口...
2025-08-04 17:00:50,946 - INFO - 微信窗口已激活。
2025-08-04 17:00:50,947 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:00:57,773 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:00:57,774 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:04:53,246 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:04:53,247 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:04:53,254 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:04:53,254 - INFO - ================================================================================
2025-08-04 17:04:53,254 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:04:53,254 - INFO - ================================================================================
2025-08-04 17:04:53,254 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:04:53,331 - INFO - 找到有效目标 1: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 2: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 3: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-04 17:04:53,332 - INFO - 找到有效目标 4: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-04 17:04:53,333 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:04:53,333 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:04:53,333 - INFO - ============================================================
2025-08-04 17:04:53,333 - INFO - 📍 处理第 1/4 个公众号: 南京工会会员
2025-08-04 17:04:53,333 - INFO - ============================================================
2025-08-04 17:04:53,333 - INFO - [步骤 1/5] 为 '南京工会会员' 创建独立的 Cookie 抓取器...
2025-08-04 17:04:53,333 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:04:53,333 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:04:53,333 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:04:53,333 - INFO - === 开始重置网络状态 ===
2025-08-04 17:04:53,333 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:04:53,454 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:04:53,455 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:04:53,455 - INFO - 系统代理已成功关闭
2025-08-04 17:04:53,455 - INFO - ✅ 代理关闭操作
2025-08-04 17:04:53,455 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:04:57,597 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:04:57,598 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:04:57,598 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:04:57,598 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:04:57,599 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:04:58,101 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:04:58,102 - INFO - 🔄 Cookie抓取器进程已启动，PID: 2404
2025-08-04 17:04:58,102 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:05:01,102 - INFO - 等待代理服务启动...
2025-08-04 17:05:01,688 - INFO - 代理服务已启动并正常工作
2025-08-04 17:05:01,688 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 2404)
2025-08-04 17:05:01,689 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:05:01,689 - INFO - [步骤 2/5] 为 '南京工会会员' 启动 UI 自动化...
2025-08-04 17:05:01,689 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:05:01,689 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:05:01,689 - INFO - 正在查找微信主窗口...
2025-08-04 17:05:03,003 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:05:03,003 - INFO - 正在激活微信窗口...
2025-08-04 17:05:05,519 - INFO - 微信窗口已激活。
2025-08-04 17:05:05,520 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:05:12,336 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:05:12,337 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:05:15,671 - INFO - 正在查找聊天输入框...
2025-08-04 17:05:17,672 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:05:17,687 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:05:17,688 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:05:19,291 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&mid=2247667614&idx=1&sn=4488f6e1afcbdb1b49eec9f270562f5e&chksm=ec4b9db7db3c14a1943b9f0e52b6650c218685563b802583089ce84c48d54580c69e663b934c#rd
2025-08-04 17:05:21,585 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:05:21,832 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:05:22,638 - INFO - 链接已发送。
2025-08-04 17:05:25,639 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:05:27,739 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:05:28,472 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:05:31,473 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:05:31,474 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:05:31,474 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:05:31,478 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:05:34,184 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:05:35,685 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:05:43,081 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:05:43,081 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:05:43,081 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:05:43,082 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:05:43,082 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:43,083 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:05:43,083 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:05:43,083 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:43,083 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:05:43,083 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:05:43,083 - INFO - [步骤 3/5] 等待 '南京工会会员' 的 Cookie 数据...
2025-08-04 17:05:43,083 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:05:44,084 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:05:44,085 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:05:44,086 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:05:44,086 - INFO - [步骤 4/5] 停止 '南京工会会员' 的 Cookie 抓取器...
2025-08-04 17:05:44,087 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:05:44,087 - INFO - 正在停止Cookie抓取器 (PID: 2404)...
2025-08-04 17:05:44,089 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:05:44,090 - INFO - 正在验证并清理代理设置...
2025-08-04 17:05:44,090 - INFO - === 开始重置网络状态 ===
2025-08-04 17:05:44,090 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:05:44,231 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:05:44,231 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:05:44,231 - INFO - 系统代理已成功关闭
2025-08-04 17:05:44,231 - INFO - ✅ 代理关闭操作
2025-08-04 17:05:44,232 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:05:45,167 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:05:45,168 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:05:45,169 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:05:46,118 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:05:46,119 - INFO - ✅ 网络连接验证正常
2025-08-04 17:05:49,119 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:05:49,120 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:06:04,121 - INFO - [步骤 5/5] 开始爬取 '南京工会会员' 的文章...
2025-08-04 17:06:04,121 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:06:04,256 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:06:14,256 - INFO - 🔄 尝试第 2/3 次爬取...
2025-08-04 17:06:14,444 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:06:24,445 - INFO - 🔄 尝试第 3/3 次爬取...
2025-08-04 17:06:24,574 - ERROR - ❌ 所有重试都失败了
2025-08-04 17:06:24,574 - WARNING - ⚠️ 公众号 '南京工会会员' 未获取到任何文章数据
2025-08-04 17:06:24,575 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 17:06:39,575 - INFO - ============================================================
2025-08-04 17:06:39,576 - INFO - 📍 处理第 2/4 个公众号: 青春南京
2025-08-04 17:06:39,576 - INFO - ============================================================
2025-08-04 17:06:39,577 - INFO - [步骤 1/5] 为 '青春南京' 创建独立的 Cookie 抓取器...
2025-08-04 17:06:39,578 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:06:39,579 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:06:39,579 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:06:39,579 - INFO - === 开始重置网络状态 ===
2025-08-04 17:06:39,580 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:06:39,684 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:06:39,684 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:06:39,684 - INFO - 系统代理已成功关闭
2025-08-04 17:06:39,684 - INFO - ✅ 代理关闭操作
2025-08-04 17:06:39,685 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:06:42,586 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:06:42,587 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:06:42,587 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:06:42,588 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:06:42,589 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:06:43,097 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:06:43,097 - INFO - 🔄 Cookie抓取器进程已启动，PID: 18748
2025-08-04 17:06:43,098 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:06:46,098 - INFO - 等待代理服务启动...
2025-08-04 17:06:46,578 - INFO - 代理服务已启动并正常工作
2025-08-04 17:06:46,579 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 18748)
2025-08-04 17:06:46,579 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:06:46,580 - INFO - [步骤 2/5] 为 '青春南京' 启动 UI 自动化...
2025-08-04 17:06:46,581 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:06:46,582 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:06:46,582 - INFO - 正在查找微信主窗口...
2025-08-04 17:06:46,978 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:06:46,978 - INFO - 正在激活微信窗口...
2025-08-04 17:06:49,491 - INFO - 微信窗口已激活。
2025-08-04 17:06:49,491 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:06:56,209 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:06:56,210 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:06:59,538 - INFO - 正在查找聊天输入框...
2025-08-04 17:07:01,538 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:07:01,549 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:07:01,549 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:07:03,118 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&mid=2650568247&idx=1&sn=f122dfa78fd5d79d0413c48c7f2f664a&chksm=be88323589ffbb23a3b8693c03b2867b211309e68758c5f85d3e15f4dc8dba0d5480a3412abc#rd
2025-08-04 17:07:05,408 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:07:05,656 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:07:06,471 - INFO - 链接已发送。
2025-08-04 17:07:09,472 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:07:11,567 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:07:12,304 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:07:15,305 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:07:15,306 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:07:15,306 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:07:15,311 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:07:18,016 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:07:19,518 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:07:26,896 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:07:26,897 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:07:26,897 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:07:26,897 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:07:26,897 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:26,897 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:07:26,897 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:07:26,897 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:26,898 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:07:26,898 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:07:26,898 - INFO - [步骤 3/5] 等待 '青春南京' 的 Cookie 数据...
2025-08-04 17:07:26,898 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:07:27,898 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:07:27,900 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:07:27,901 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:07:27,902 - INFO - [步骤 4/5] 停止 '青春南京' 的 Cookie 抓取器...
2025-08-04 17:07:27,902 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:07:27,903 - INFO - 正在停止Cookie抓取器 (PID: 18748)...
2025-08-04 17:07:27,905 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:07:27,905 - INFO - 正在验证并清理代理设置...
2025-08-04 17:07:27,906 - INFO - === 开始重置网络状态 ===
2025-08-04 17:07:27,906 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:07:28,005 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:07:28,005 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:07:28,005 - INFO - 系统代理已成功关闭
2025-08-04 17:07:28,005 - INFO - ✅ 代理关闭操作
2025-08-04 17:07:28,005 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:07:31,056 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:07:31,056 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:07:31,056 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:07:31,945 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:07:31,946 - INFO - ✅ 网络连接验证正常
2025-08-04 17:07:34,946 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:07:34,947 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:07:49,948 - INFO - [步骤 5/5] 开始爬取 '青春南京' 的文章...
2025-08-04 17:07:49,949 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:07:50,058 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:08:00,059 - INFO - 🔄 尝试第 2/3 次爬取...
2025-08-04 17:08:00,164 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:08:10,165 - INFO - 🔄 尝试第 3/3 次爬取...
2025-08-04 17:08:10,353 - ERROR - ❌ 所有重试都失败了
2025-08-04 17:08:10,354 - WARNING - ⚠️ 公众号 '青春南京' 未获取到任何文章数据
2025-08-04 17:08:10,354 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 17:08:17,158 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:08:17,159 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:08:17,164 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:08:17,164 - INFO - ================================================================================
2025-08-04 17:08:17,164 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:08:17,164 - INFO - ================================================================================
2025-08-04 17:08:17,164 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:08:17,240 - INFO - 找到有效目标 1: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-04 17:08:17,242 - INFO - 找到有效目标 2: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-04 17:08:17,243 - INFO - 找到有效目标 3: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-04 17:08:17,243 - INFO - 找到有效目标 4: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-04 17:08:17,243 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 17:08:17,243 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 17:08:17,243 - INFO - ============================================================
2025-08-04 17:08:17,243 - INFO - 📍 处理第 1/4 个公众号: 南京工会会员
2025-08-04 17:08:17,243 - INFO - ============================================================
2025-08-04 17:08:17,243 - INFO - [步骤 1/5] 为 '南京工会会员' 创建独立的 Cookie 抓取器...
2025-08-04 17:08:17,244 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:08:17,244 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:08:17,244 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:08:17,244 - INFO - === 开始重置网络状态 ===
2025-08-04 17:08:17,244 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:08:17,359 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:08:17,360 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:08:17,360 - INFO - 系统代理已成功关闭
2025-08-04 17:08:17,360 - INFO - ✅ 代理关闭操作
2025-08-04 17:08:17,360 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:08:20,220 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:08:20,221 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:08:20,222 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:08:20,222 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:08:20,223 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:08:20,756 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:08:20,757 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33800
2025-08-04 17:08:20,757 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:08:23,757 - INFO - 等待代理服务启动...
2025-08-04 17:08:24,222 - INFO - 代理服务已启动并正常工作
2025-08-04 17:08:24,222 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33800)
2025-08-04 17:08:24,223 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:08:24,223 - INFO - [步骤 2/5] 为 '南京工会会员' 启动 UI 自动化...
2025-08-04 17:08:24,223 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:08:24,224 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:08:24,224 - INFO - 正在查找微信主窗口...
2025-08-04 17:08:25,326 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:08:25,326 - INFO - 正在激活微信窗口...
2025-08-04 17:08:27,840 - INFO - 微信窗口已激活。
2025-08-04 17:08:27,841 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:08:34,539 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:08:34,539 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:08:37,864 - INFO - 正在查找聊天输入框...
2025-08-04 17:08:39,865 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:08:39,876 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:08:39,876 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:08:41,461 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&mid=2247667614&idx=1&sn=4488f6e1afcbdb1b49eec9f270562f5e&chksm=ec4b9db7db3c14a1943b9f0e52b6650c218685563b802583089ce84c48d54580c69e663b934c#rd
2025-08-04 17:08:43,751 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:08:43,994 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:08:44,805 - INFO - 链接已发送。
2025-08-04 17:08:47,806 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:08:49,898 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:08:50,620 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:08:53,622 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:08:53,622 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:08:53,622 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:08:53,625 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:08:56,329 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:08:57,830 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:09:05,221 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:09:05,221 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:09:05,221 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:09:05,221 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:09:05,222 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:05,222 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:09:05,222 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:09:05,222 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:05,222 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:09:05,222 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:09:05,222 - INFO - [步骤 3/5] 等待 '南京工会会员' 的 Cookie 数据...
2025-08-04 17:09:05,222 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:09:06,223 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:09:06,223 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:09:06,224 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:09:06,224 - INFO - [步骤 4/5] 停止 '南京工会会员' 的 Cookie 抓取器...
2025-08-04 17:09:06,224 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:09:06,225 - INFO - 正在停止Cookie抓取器 (PID: 33800)...
2025-08-04 17:09:06,226 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:09:06,227 - INFO - 正在验证并清理代理设置...
2025-08-04 17:09:06,227 - INFO - === 开始重置网络状态 ===
2025-08-04 17:09:06,227 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:09:06,328 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:09:06,328 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:09:06,328 - INFO - 系统代理已成功关闭
2025-08-04 17:09:06,329 - INFO - ✅ 代理关闭操作
2025-08-04 17:09:06,329 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:09:07,244 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:09:07,245 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:09:07,245 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:09:10,094 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:09:10,094 - INFO - ✅ 网络连接验证正常
2025-08-04 17:09:13,095 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:09:13,095 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:09:28,096 - INFO - [步骤 5/5] 开始爬取 '南京工会会员' 的文章...
2025-08-04 17:09:28,096 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:09:28,573 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-04 17:10:57,170 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:10:57,170 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:10:57,175 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:10:57,176 - INFO - ================================================================================
2025-08-04 17:10:57,176 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:10:57,176 - INFO - ================================================================================
2025-08-04 17:10:57,176 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:10:57,256 - INFO - 找到有效目标 1: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-04 17:10:57,258 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:10:57,258 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:10:57,258 - INFO - ============================================================
2025-08-04 17:10:57,259 - INFO - 📍 处理第 1/1 个公众号: 南京税务
2025-08-04 17:10:57,259 - INFO - ============================================================
2025-08-04 17:10:57,259 - INFO - [步骤 1/5] 为 '南京税务' 创建独立的 Cookie 抓取器...
2025-08-04 17:10:57,259 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:10:57,259 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:10:57,259 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:10:57,259 - INFO - === 开始重置网络状态 ===
2025-08-04 17:10:57,259 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:10:57,349 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:10:57,349 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:10:57,350 - INFO - 系统代理已成功关闭
2025-08-04 17:10:57,350 - INFO - ✅ 代理关闭操作
2025-08-04 17:10:57,350 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:10:58,292 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:10:58,292 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:10:58,292 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:10:58,293 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:10:58,293 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:10:58,786 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:10:58,787 - INFO - 🔄 Cookie抓取器进程已启动，PID: 35612
2025-08-04 17:10:58,787 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:11:01,787 - INFO - 等待代理服务启动...
2025-08-04 17:11:02,243 - INFO - 代理服务已启动并正常工作
2025-08-04 17:11:02,243 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 35612)
2025-08-04 17:11:02,243 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:11:02,243 - INFO - [步骤 2/5] 为 '南京税务' 启动 UI 自动化...
2025-08-04 17:11:02,244 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:11:02,244 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:11:02,244 - INFO - 正在查找微信主窗口...
2025-08-04 17:11:03,231 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:11:03,231 - INFO - 正在激活微信窗口...
2025-08-04 17:11:05,737 - INFO - 微信窗口已激活。
2025-08-04 17:11:05,737 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:11:12,455 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:11:12,456 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:11:15,787 - INFO - 正在查找聊天输入框...
2025-08-04 17:11:17,788 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:11:17,792 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:11:17,793 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:11:19,383 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&mid=2650808386&idx=1&sn=968d6981cb041399053104db0fd8ca79&chksm=bd05b6488a723f5e7243ed8f14aa4f93acc11b592c6e837bfac98061b8740f039d4f3305182f#rd
2025-08-04 17:11:21,673 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:11:21,928 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:11:22,737 - INFO - 链接已发送。
2025-08-04 17:11:25,738 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:11:27,826 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:11:28,553 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:11:31,554 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:11:31,555 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:11:31,556 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:11:31,562 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:11:34,268 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:11:35,769 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:11:43,133 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:11:43,133 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:11:43,133 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:11:43,133 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:11:43,133 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:43,134 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:11:43,134 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:11:43,134 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:43,134 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:11:43,134 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:11:43,134 - INFO - [步骤 3/5] 等待 '南京税务' 的 Cookie 数据...
2025-08-04 17:11:43,134 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:11:44,135 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:11:44,137 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:11:44,137 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:11:44,138 - INFO - [步骤 4/5] 停止 '南京税务' 的 Cookie 抓取器...
2025-08-04 17:11:44,138 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:11:44,139 - INFO - 正在停止Cookie抓取器 (PID: 35612)...
2025-08-04 17:11:44,141 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:11:44,141 - INFO - 正在验证并清理代理设置...
2025-08-04 17:11:44,141 - INFO - === 开始重置网络状态 ===
2025-08-04 17:11:44,142 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:11:44,269 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:11:44,269 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:11:44,269 - INFO - 系统代理已成功关闭
2025-08-04 17:11:44,269 - INFO - ✅ 代理关闭操作
2025-08-04 17:11:44,269 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:11:46,057 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:11:46,058 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:11:46,058 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:11:47,770 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:11:47,771 - INFO - ✅ 网络连接验证正常
2025-08-04 17:11:50,772 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:11:50,773 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:12:05,774 - INFO - [步骤 5/5] 开始爬取 '南京税务' 的文章...
2025-08-04 17:12:05,775 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:12:05,889 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:18:32,435 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:18:32,435 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:18:32,440 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:18:32,440 - INFO - ================================================================================
2025-08-04 17:18:32,440 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:18:32,441 - INFO - ================================================================================
2025-08-04 17:18:32,441 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:18:32,515 - INFO - 找到有效目标 1: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-04 17:18:32,517 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:18:32,517 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:18:32,517 - INFO - ============================================================
2025-08-04 17:18:32,517 - INFO - 📍 处理第 1/1 个公众号: 南京税务
2025-08-04 17:18:32,517 - INFO - ============================================================
2025-08-04 17:18:32,517 - INFO - [步骤 1/5] 为 '南京税务' 创建独立的 Cookie 抓取器...
2025-08-04 17:18:32,518 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:18:32,518 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:18:32,518 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:18:32,518 - INFO - === 开始重置网络状态 ===
2025-08-04 17:18:32,518 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:18:32,608 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:18:32,608 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:18:32,608 - INFO - 系统代理已成功关闭
2025-08-04 17:18:32,608 - INFO - ✅ 代理关闭操作
2025-08-04 17:18:32,609 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:21:27,352 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-04 17:21:27,352 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-04 17:21:27,356 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:21:27,356 - INFO - ================================================================================
2025-08-04 17:21:27,356 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:21:27,357 - INFO - ================================================================================
2025-08-04 17:21:27,357 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:21:27,428 - INFO - 找到有效目标 1: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-04 17:21:27,429 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:21:27,430 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:21:27,430 - INFO - ============================================================
2025-08-04 17:21:27,430 - INFO - 📍 处理第 1/1 个公众号: 南京税务
2025-08-04 17:21:27,430 - INFO - ============================================================
2025-08-04 17:21:27,430 - INFO - [步骤 1/5] 为 '南京税务' 创建独立的 Cookie 抓取器...
2025-08-04 17:21:27,430 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:21:27,430 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:21:27,431 - INFO - === 开始重置网络状态 ===
2025-08-04 17:21:27,431 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:21:27,519 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:21:27,519 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:21:27,519 - INFO - 系统代理已成功关闭
2025-08-04 17:21:27,519 - INFO - ✅ 代理关闭操作
2025-08-04 17:21:27,519 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:21:29,323 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:21:29,324 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:21:29,324 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:21:29,324 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:21:29,325 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:21:29,849 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:21:29,850 - INFO - 🔄 Cookie抓取器进程已启动，PID: 36020
2025-08-04 17:21:29,850 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:21:32,850 - INFO - 等待代理服务启动...
2025-08-04 17:21:33,323 - INFO - 代理服务已启动并正常工作
2025-08-04 17:21:33,323 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 36020)
2025-08-04 17:21:33,324 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:21:33,324 - INFO - [步骤 2/5] 为 '南京税务' 启动 UI 自动化...
2025-08-04 17:21:33,325 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:21:33,325 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:21:33,325 - INFO - 正在查找微信主窗口...
2025-08-04 17:21:35,423 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:21:35,423 - INFO - 正在激活微信窗口...
2025-08-04 17:21:37,935 - INFO - 微信窗口已激活。
2025-08-04 17:21:37,935 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:21:44,877 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:21:44,878 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:21:48,222 - INFO - 正在查找聊天输入框...
2025-08-04 17:21:50,223 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:21:50,231 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:21:50,232 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:21:51,819 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&mid=2650808386&idx=1&sn=968d6981cb041399053104db0fd8ca79&chksm=bd05b6488a723f5e7243ed8f14aa4f93acc11b592c6e837bfac98061b8740f039d4f3305182f#rd
2025-08-04 17:21:54,108 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:21:54,360 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:21:55,170 - INFO - 链接已发送。
2025-08-04 17:21:58,172 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:22:00,262 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:22:00,986 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:22:03,987 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:22:03,988 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:22:03,989 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:22:03,996 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:22:06,701 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:22:08,203 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:22:14,517 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:22:14,518 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:22:14,518 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:22:14,518 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:22:14,518 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:22:14,518 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:22:14,518 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:22:14,519 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:22:14,519 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:22:14,519 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:22:14,519 - INFO - [步骤 3/5] 等待 '南京税务' 的 Cookie 数据...
2025-08-04 17:22:14,519 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:22:15,520 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:22:15,521 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:22:15,522 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:22:15,522 - INFO - [步骤 4/5] 停止 '南京税务' 的 Cookie 抓取器...
2025-08-04 17:22:15,522 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:22:15,522 - INFO - 正在停止Cookie抓取器 (PID: 36020)...
2025-08-04 17:22:15,524 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:22:15,524 - INFO - 正在验证并清理代理设置...
2025-08-04 17:22:15,524 - INFO - === 开始重置网络状态 ===
2025-08-04 17:22:15,524 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:22:15,636 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:22:15,636 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:22:15,636 - INFO - 系统代理已成功关闭
2025-08-04 17:22:15,636 - INFO - ✅ 代理关闭操作
2025-08-04 17:22:15,636 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:22:17,116 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:22:17,117 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:22:17,118 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:22:19,754 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:22:19,754 - INFO - ✅ 网络连接验证正常
2025-08-04 17:22:22,755 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:22:22,755 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:22:37,756 - INFO - [步骤 5/5] 开始爬取 '南京税务' 的文章...
2025-08-04 17:22:37,757 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:22:38,041 - INFO - ✅ Cookie验证成功，开始正式爬取...
