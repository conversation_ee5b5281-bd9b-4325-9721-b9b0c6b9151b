2025-08-04 17:25:26,091 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:25:26,091 - INFO - ================================================================================
2025-08-04 17:25:26,092 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:25:26,092 - INFO - ================================================================================
2025-08-04 17:25:26,092 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:25:26,163 - INFO - 找到有效目标 1: 南京市残疾人联合会 - http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&m...
2025-08-04 17:25:26,165 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:25:26,165 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:25:26,165 - INFO - ============================================================
2025-08-04 17:25:26,165 - INFO - 📍 处理第 1/1 个公众号: 南京市残疾人联合会
2025-08-04 17:25:26,165 - INFO - ============================================================
2025-08-04 17:25:26,165 - INFO - [步骤 1/5] 为 '南京市残疾人联合会' 创建独立的 Cookie 抓取器...
2025-08-04 17:25:26,166 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:25:26,166 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:25:26,166 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:25:26,166 - INFO - === 开始重置网络状态 ===
2025-08-04 17:25:26,166 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:25:26,272 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:25:26,272 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:25:26,272 - INFO - 系统代理已成功关闭
2025-08-04 17:25:26,273 - INFO - ✅ 代理关闭操作
2025-08-04 17:25:26,273 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:25:28,237 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:25:28,238 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:25:28,239 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:25:28,240 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:25:28,240 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:25:28,736 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:25:28,737 - INFO - 🔄 Cookie抓取器进程已启动，PID: 35852
2025-08-04 17:25:28,738 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:25:31,738 - INFO - 等待代理服务启动...
2025-08-04 17:25:33,299 - INFO - 代理服务已启动并正常工作
2025-08-04 17:25:33,299 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 35852)
2025-08-04 17:25:33,300 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:25:33,300 - INFO - [步骤 2/5] 为 '南京市残疾人联合会' 启动 UI 自动化...
2025-08-04 17:25:33,301 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:25:33,301 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:25:33,302 - INFO - 正在查找微信主窗口...
2025-08-04 17:25:33,965 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:25:33,966 - INFO - 正在激活微信窗口...
2025-08-04 17:25:36,479 - INFO - 微信窗口已激活。
2025-08-04 17:25:36,479 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:25:43,108 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:25:43,109 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:25:46,435 - INFO - 正在查找聊天输入框...
2025-08-04 17:25:48,437 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:25:48,451 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:25:48,451 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:25:50,033 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&mid=2247567892&idx=1&sn=9639c9464cea66b9caa7d7d316b094f9&chksm=fc7bec9ccb0c658a065a4fd8be47fd22591cc463e3972acc708e89186d370175a0c880db0455#rd
2025-08-04 17:25:52,323 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:25:52,565 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:25:53,372 - INFO - 链接已发送。
2025-08-04 17:25:56,374 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:25:58,462 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:25:59,189 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:26:02,189 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:26:02,190 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:26:02,190 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:26:02,193 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:26:04,899 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:26:06,400 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:26:13,480 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:26:13,481 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:26:13,481 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:26:13,481 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:26:13,481 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:26:13,481 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:26:13,481 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:26:13,482 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:26:13,482 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:26:13,482 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:26:13,482 - INFO - [步骤 3/5] 等待 '南京市残疾人联合会' 的 Cookie 数据...
2025-08-04 17:26:13,482 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:26:14,483 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:26:14,485 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:26:14,485 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:26:14,486 - INFO - [步骤 4/5] 停止 '南京市残疾人联合会' 的 Cookie 抓取器...
2025-08-04 17:26:14,487 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:26:14,487 - INFO - 正在停止Cookie抓取器 (PID: 35852)...
2025-08-04 17:26:14,490 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:26:14,491 - INFO - 正在验证并清理代理设置...
2025-08-04 17:26:14,492 - INFO - === 开始重置网络状态 ===
2025-08-04 17:26:14,492 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:26:14,593 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:26:14,593 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:26:14,593 - INFO - 系统代理已成功关闭
2025-08-04 17:26:14,593 - INFO - ✅ 代理关闭操作
2025-08-04 17:26:14,593 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:26:16,870 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:26:16,871 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:26:16,872 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:26:19,041 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:26:19,042 - INFO - ✅ 网络连接验证正常
2025-08-04 17:26:22,042 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:26:22,043 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:26:37,044 - INFO - [步骤 5/5] 开始爬取 '南京市残疾人联合会' 的文章...
2025-08-04 17:26:37,046 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:26:37,385 - INFO - ✅ Cookie验证成功，开始正式爬取...
