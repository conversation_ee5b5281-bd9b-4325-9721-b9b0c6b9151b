2025-08-04 16:10:02,757 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:10:02,757 - INFO - ================================================================================
2025-08-04 16:10:02,757 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:10:02,757 - INFO - ================================================================================
2025-08-04 16:10:02,757 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:10:02,829 - INFO - 找到有效目标 1: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 2: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 3: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-04 16:10:02,831 - INFO - 找到有效目标 4: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-04 16:10:02,831 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:10:02,831 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:10:02,831 - INFO - ============================================================
2025-08-04 16:10:02,831 - INFO - 📍 处理第 1/4 个公众号: 南京市城管局
2025-08-04 16:10:02,832 - INFO - ============================================================
2025-08-04 16:10:02,832 - INFO - [步骤 1/5] 为 '南京市城管局' 启动 Cookie 抓取器...
2025-08-04 16:10:02,832 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:10:02,832 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:10:02,832 - INFO - === 开始重置网络状态 ===
2025-08-04 16:10:02,832 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:10:02,920 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:10:02,920 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:10:02,920 - INFO - 系统代理已成功关闭
2025-08-04 16:10:02,920 - INFO - ✅ 代理关闭操作
2025-08-04 16:10:02,920 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:10:05,382 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:05,383 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:10:05,383 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:10:05,383 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:10:05,383 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:10:05,886 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:10:05,887 - INFO - 🔄 Cookie抓取器进程已启动，PID: 25844
2025-08-04 16:10:05,887 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:10:08,887 - INFO - 等待代理服务启动...
2025-08-04 16:10:10,598 - INFO - 代理服务已启动并正常工作
2025-08-04 16:10:10,599 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 25844)
2025-08-04 16:10:10,599 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:10:10,600 - INFO - [步骤 2/5] 为 '南京市城管局' 启动 UI 自动化...
2025-08-04 16:10:10,600 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:10:10,601 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:10:10,601 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:10:10,601 - INFO - 正在查找微信主窗口...
2025-08-04 16:10:11,407 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:10:11,408 - INFO - 正在激活微信窗口...
2025-08-04 16:10:13,920 - INFO - 微信窗口已激活。
2025-08-04 16:10:13,921 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:10:20,613 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:10:20,614 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:10:23,952 - INFO - 正在查找聊天输入框...
2025-08-04 16:10:25,954 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:10:25,960 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:10:25,960 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:10:27,539 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&mid=2247564011&idx=1&sn=e94d40f840abcb2aedaf3f93eafa8372&chksm=eb4b7732dc3cfe24e9d2c8c4075e9d8b23edd57f84c7e6ff8510365992e761a0d21e2988bc70#rd
2025-08-04 16:10:29,828 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:10:30,035 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:10:30,835 - INFO - 链接已发送。
2025-08-04 16:10:33,835 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:10:35,942 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:10:36,670 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:10:39,671 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:10:39,672 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:10:39,672 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:10:39,683 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:10:42,390 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:10:43,891 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:10:51,891 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:10:51,891 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:10:51,891 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:10:51,891 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:10:51,892 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:51,892 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:10:51,892 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:10:51,892 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:51,892 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:10:51,892 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:10:51,892 - INFO - [步骤 3/5] 等待 '南京市城管局' 的 Cookie 数据...
2025-08-04 16:10:51,893 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:10:52,893 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:10:52,894 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:10:52,895 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:10:52,896 - INFO - [步骤 4/5] 停止 '南京市城管局' 的 Cookie 抓取器...
2025-08-04 16:10:52,896 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:10:52,897 - INFO - 正在停止Cookie抓取器 (PID: 25844)...
2025-08-04 16:10:52,901 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:10:52,901 - INFO - 正在验证并清理代理设置...
2025-08-04 16:10:52,902 - INFO - === 开始重置网络状态 ===
2025-08-04 16:10:52,902 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:10:52,993 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:10:52,993 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:10:52,993 - INFO - 系统代理已成功关闭
2025-08-04 16:10:52,993 - INFO - ✅ 代理关闭操作
2025-08-04 16:10:52,993 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:10:54,461 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:54,462 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:10:54,462 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:10:56,319 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:10:56,320 - INFO - ✅ 网络连接验证正常
2025-08-04 16:10:59,321 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:10:59,321 - INFO - [步骤 5/5] 开始爬取 '南京市城管局' 的文章...
2025-08-04 16:11:42,347 - INFO - ✅ 公众号 '南京市城管局' 爬取完成！获取 5 篇文章
2025-08-04 16:11:42,347 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京市城管局_20250804_161142.xlsx
2025-08-04 16:11:42,347 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:11:57,348 - INFO - ============================================================
2025-08-04 16:11:57,349 - INFO - 📍 处理第 2/4 个公众号: 南京美丽乡村
2025-08-04 16:11:57,350 - INFO - ============================================================
2025-08-04 16:11:57,350 - INFO - [步骤 1/5] 为 '南京美丽乡村' 启动 Cookie 抓取器...
2025-08-04 16:11:57,350 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:11:57,351 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:11:57,351 - INFO - === 开始重置网络状态 ===
2025-08-04 16:11:57,351 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:11:57,466 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:11:57,466 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:11:57,467 - INFO - 系统代理已成功关闭
2025-08-04 16:11:57,467 - INFO - ✅ 代理关闭操作
2025-08-04 16:11:57,467 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:11:59,129 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:11:59,129 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:11:59,129 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:11:59,129 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:11:59,130 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:11:59,672 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:11:59,673 - INFO - 🔄 Cookie抓取器进程已启动，PID: 7512
2025-08-04 16:11:59,673 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:12:02,673 - INFO - 等待代理服务启动...
2025-08-04 16:12:07,799 - INFO - 代理服务已启动并正常工作
2025-08-04 16:12:07,800 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 7512)
2025-08-04 16:12:07,800 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:12:07,801 - INFO - [步骤 2/5] 为 '南京美丽乡村' 启动 UI 自动化...
2025-08-04 16:12:07,801 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:12:07,802 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:12:07,802 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:12:07,803 - INFO - 正在查找微信主窗口...
2025-08-04 16:12:08,061 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:12:08,061 - INFO - 正在激活微信窗口...
2025-08-04 16:12:10,575 - INFO - 微信窗口已激活。
2025-08-04 16:12:10,576 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:12:17,242 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:12:17,243 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:12:20,570 - INFO - 正在查找聊天输入框...
2025-08-04 16:12:22,571 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:12:22,578 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:12:22,578 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:12:24,144 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&mid=2247521900&idx=1&sn=7718b37c481a1948c6ff820192cf3207&chksm=97617896a016f180f83bc89bd8f8ae968e92b0b67273d0c0f71340d2a34bcb92436000b6e147#rd
2025-08-04 16:12:26,431 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:12:26,649 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:12:27,335 - INFO - 链接已发送。
2025-08-04 16:12:30,337 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:12:32,426 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:12:33,152 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:12:36,154 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:12:36,154 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:12:36,155 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:12:36,166 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:12:38,872 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:12:40,373 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:12:48,386 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:12:48,386 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:12:48,386 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:12:48,386 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:12:48,387 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:48,387 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:12:48,387 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:12:48,387 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:48,387 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:12:48,387 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:12:48,387 - INFO - [步骤 3/5] 等待 '南京美丽乡村' 的 Cookie 数据...
2025-08-04 16:12:48,387 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:12:49,388 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:12:49,388 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:12:49,389 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:12:49,390 - INFO - [步骤 4/5] 停止 '南京美丽乡村' 的 Cookie 抓取器...
2025-08-04 16:12:49,390 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:12:49,390 - INFO - 正在停止Cookie抓取器 (PID: 7512)...
2025-08-04 16:12:49,393 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:12:49,393 - INFO - 正在验证并清理代理设置...
2025-08-04 16:12:49,394 - INFO - === 开始重置网络状态 ===
2025-08-04 16:12:49,395 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:12:49,498 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:12:49,499 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:12:49,499 - INFO - 系统代理已成功关闭
2025-08-04 16:12:49,499 - INFO - ✅ 代理关闭操作
2025-08-04 16:12:49,499 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:12:51,746 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:12:51,747 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:12:51,748 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:12:54,114 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:12:54,114 - INFO - ✅ 网络连接验证正常
2025-08-04 16:12:57,115 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:12:57,115 - INFO - [步骤 5/5] 开始爬取 '南京美丽乡村' 的文章...
2025-08-04 16:13:41,579 - INFO - ✅ 公众号 '南京美丽乡村' 爬取完成！获取 5 篇文章
2025-08-04 16:13:41,579 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_南京美丽乡村_20250804_161341.xlsx
2025-08-04 16:13:41,579 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:13:56,580 - INFO - ============================================================
2025-08-04 16:13:56,581 - INFO - 📍 处理第 3/4 个公众号: 南京应急管理
2025-08-04 16:13:56,582 - INFO - ============================================================
2025-08-04 16:13:56,583 - INFO - [步骤 1/5] 为 '南京应急管理' 启动 Cookie 抓取器...
2025-08-04 16:13:56,584 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:13:56,585 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:13:56,585 - INFO - === 开始重置网络状态 ===
2025-08-04 16:13:56,586 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:13:56,699 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:13:56,699 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:13:56,699 - INFO - 系统代理已成功关闭
2025-08-04 16:13:56,699 - INFO - ✅ 代理关闭操作
2025-08-04 16:13:56,699 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:13:57,893 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:13:57,894 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:13:57,894 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:13:57,895 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:13:57,895 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:13:58,425 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:13:58,426 - INFO - 🔄 Cookie抓取器进程已启动，PID: 27064
2025-08-04 16:13:58,426 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:14:01,426 - INFO - 等待代理服务启动...
2025-08-04 16:14:06,107 - INFO - 代理服务已启动并正常工作
2025-08-04 16:14:06,108 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 27064)
2025-08-04 16:14:06,108 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:14:06,108 - INFO - [步骤 2/5] 为 '南京应急管理' 启动 UI 自动化...
2025-08-04 16:14:06,108 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:14:06,109 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:14:06,109 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:14:06,109 - INFO - 正在查找微信主窗口...
2025-08-04 16:14:06,657 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:14:06,657 - INFO - 正在激活微信窗口...
2025-08-04 16:14:09,171 - INFO - 微信窗口已激活。
2025-08-04 16:14:09,172 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:14:15,671 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:14:15,671 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:14:19,002 - INFO - 正在查找聊天输入框...
2025-08-04 16:14:21,003 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:14:21,012 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:14:21,012 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:14:22,581 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&mid=2247688440&idx=1&sn=1b728e419d1fcbadc2c2e132bd18e976&chksm=e9b27d1ddec5f40b58ab6491fe0f56b49c4fce971a3a7f633241cf66c0317edfa5364d007f61#rd
2025-08-04 16:14:24,866 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:14:25,073 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:14:25,886 - INFO - 链接已发送。
2025-08-04 16:14:28,887 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:14:30,973 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:14:31,701 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:14:34,702 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:14:34,703 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:14:34,703 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:14:34,710 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:14:37,415 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:14:38,916 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:14:46,926 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:14:46,926 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:14:46,927 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:14:46,927 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:14:46,927 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:46,927 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:14:46,927 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:14:46,927 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:46,927 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:14:46,928 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:14:46,928 - INFO - [步骤 3/5] 等待 '南京应急管理' 的 Cookie 数据...
2025-08-04 16:14:46,928 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:14:47,929 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:14:47,930 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:14:47,931 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:14:47,931 - INFO - [步骤 4/5] 停止 '南京应急管理' 的 Cookie 抓取器...
2025-08-04 16:14:47,932 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:14:47,933 - INFO - 正在停止Cookie抓取器 (PID: 27064)...
2025-08-04 16:14:47,935 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:14:47,935 - INFO - 正在验证并清理代理设置...
2025-08-04 16:14:47,935 - INFO - === 开始重置网络状态 ===
2025-08-04 16:14:47,935 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:14:48,025 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:14:48,026 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:14:48,026 - INFO - 系统代理已成功关闭
2025-08-04 16:14:48,026 - INFO - ✅ 代理关闭操作
2025-08-04 16:14:48,026 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:14:48,999 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:14:49,000 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:14:49,001 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:14:51,170 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:14:51,171 - INFO - ✅ 网络连接验证正常
2025-08-04 16:14:54,171 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:14:54,172 - INFO - [步骤 5/5] 开始爬取 '南京应急管理' 的文章...
2025-08-04 16:14:54,277 - WARNING - ⚠️ 公众号 '南京应急管理' 未获取到任何文章数据
2025-08-04 16:14:54,277 - INFO - ⏳ 公众号间延迟 15 秒...
