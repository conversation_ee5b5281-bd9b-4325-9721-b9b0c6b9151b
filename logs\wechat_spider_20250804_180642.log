2025-08-04 18:06:42,386 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 18:06:42,387 - INFO - ================================================================================
2025-08-04 18:06:42,387 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 18:06:42,387 - INFO - ================================================================================
2025-08-04 18:06:42,387 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 18:06:42,456 - INFO - 找到有效目标 1: 江北新区综合治理 - http://mp.weixin.qq.com/s?__biz=MzU5NzgzNDIxMg==&m...
2025-08-04 18:06:42,458 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 18:06:42,458 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 18:06:42,458 - INFO - ============================================================
2025-08-04 18:06:42,458 - INFO - 📍 处理第 1/1 个公众号: 江北新区综合治理
2025-08-04 18:06:42,458 - INFO - ============================================================
2025-08-04 18:06:42,459 - INFO - [步骤 1/5] 为 '江北新区综合治理' 创建独立的 Cookie 抓取器...
2025-08-04 18:06:42,459 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 18:06:42,459 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 18:06:42,459 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 18:06:42,459 - INFO - === 开始重置网络状态 ===
2025-08-04 18:06:42,459 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:06:42,551 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:06:42,551 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:06:42,551 - INFO - 系统代理已成功关闭
2025-08-04 18:06:42,551 - INFO - ✅ 代理关闭操作
2025-08-04 18:06:42,551 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:06:48,051 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:06:48,052 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:06:48,053 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 18:06:48,054 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 18:06:48,054 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 18:06:48,590 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 18:06:48,590 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32440
2025-08-04 18:06:48,591 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 18:06:51,591 - INFO - 等待代理服务启动...
2025-08-04 18:06:52,591 - INFO - 代理服务已启动并正常工作
2025-08-04 18:06:52,592 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32440)
2025-08-04 18:06:52,592 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 18:06:52,593 - INFO - [步骤 2/5] 为 '江北新区综合治理' 启动 UI 自动化...
2025-08-04 18:06:52,593 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 18:06:52,593 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 18:06:52,594 - INFO - 正在查找微信主窗口...
2025-08-04 18:06:53,599 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 18:06:53,600 - INFO - 正在激活微信窗口...
2025-08-04 18:06:56,109 - INFO - 微信窗口已激活。
2025-08-04 18:06:56,110 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 18:07:02,855 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 18:07:02,856 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 18:07:06,191 - INFO - 正在查找聊天输入框...
2025-08-04 18:07:08,192 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 18:07:08,197 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 18:07:08,198 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 18:07:09,789 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzU5NzgzNDIxMg==&mid=2247559103&idx=1&sn=2c5852e6b51648f5daacb7811d283879&chksm=fe4ec734c9394e2269d93b9e74118209c0a4b3dbad91300cb5989f2847abae51921b7d869bfa#rd
2025-08-04 18:07:12,082 - INFO - 链接已粘贴，正在发送...
2025-08-04 18:07:12,319 - INFO - 找到发送按钮，点击发送...
2025-08-04 18:07:13,123 - INFO - 链接已发送。
2025-08-04 18:07:16,124 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 18:07:18,212 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 18:07:18,942 - INFO - ✅ 成功点击最新链接。
2025-08-04 18:07:21,943 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 18:07:21,944 - INFO - 正在查找微信浏览器窗口...
2025-08-04 18:07:21,944 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 18:07:21,956 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 18:07:24,663 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 18:07:26,164 - INFO - 正在检测SSL证书错误页面...
2025-08-04 18:07:33,502 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 18:07:33,502 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 18:07:33,502 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 18:07:33,502 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 18:07:33,503 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:07:33,503 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 18:07:33,503 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 18:07:33,503 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:07:33,503 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 18:07:33,503 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 18:07:33,503 - INFO - [步骤 3/5] 等待 '江北新区综合治理' 的 Cookie 数据...
2025-08-04 18:07:33,503 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:07:34,504 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:07:34,505 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:07:34,506 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 18:07:34,612 - WARNING - ⚠️ Cookie无效（第1次尝试）
2025-08-04 18:07:34,613 - INFO - 🔄 Cookie无效，准备重新抓取...
2025-08-04 18:07:34,613 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 18:07:35,614 - INFO - 检测到Cookie文件已生成。
2025-08-04 18:07:35,615 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 18:07:35,616 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 18:07:35,730 - WARNING - ⚠️ Cookie无效（第2次尝试）
2025-08-04 18:07:35,731 - ERROR - ❌ 多次尝试后Cookie仍然无效
2025-08-04 18:07:35,731 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:07:35,732 - INFO - 正在停止Cookie抓取器 (PID: 32440)...
2025-08-04 18:07:35,734 - INFO - Cookie抓取器已成功终止。
2025-08-04 18:07:35,735 - INFO - 正在验证并清理代理设置...
2025-08-04 18:07:35,735 - INFO - === 开始重置网络状态 ===
2025-08-04 18:07:35,736 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:07:35,845 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:07:35,845 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:07:35,845 - INFO - 系统代理已成功关闭
2025-08-04 18:07:35,845 - INFO - ✅ 代理关闭操作
2025-08-04 18:07:35,845 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:07:36,856 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:07:36,857 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:07:36,858 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:07:39,361 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:07:39,361 - INFO - ✅ 网络连接验证正常
2025-08-04 18:07:39,362 - INFO - ✅ 成功获取并验证了有效的 Cookie。
2025-08-04 18:07:39,362 - INFO - [步骤 4/5] 停止 '江北新区综合治理' 的 Cookie 抓取器...
2025-08-04 18:07:39,363 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 18:07:39,363 - INFO - Cookie抓取器未在运行或已停止。
2025-08-04 18:07:39,363 - INFO - 正在验证并清理代理设置...
2025-08-04 18:07:39,364 - INFO - === 开始重置网络状态 ===
2025-08-04 18:07:39,364 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 18:07:39,475 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 18:07:39,475 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 18:07:39,476 - INFO - 系统代理已成功关闭
2025-08-04 18:07:39,476 - INFO - ✅ 代理关闭操作
2025-08-04 18:07:39,476 - INFO - 🔗 正在验证网络连接...
2025-08-04 18:07:41,329 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:07:41,330 - INFO - ✅ 网络状态重置验证完成
2025-08-04 18:07:41,331 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 18:07:45,003 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 18:07:45,004 - INFO - ✅ 网络连接验证正常
2025-08-04 18:07:48,005 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 18:07:48,006 - INFO - [步骤 5/5] 开始爬取 '江北新区综合治理' 的文章...
2025-08-04 18:07:48,116 - WARNING - ⚠️ 公众号 '江北新区综合治理' 未获取到任何文章数据
2025-08-04 18:07:48,116 - INFO - ================================================================================
2025-08-04 18:07:48,116 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 18:07:48,116 - INFO - ================================================================================
2025-08-04 18:07:48,116 - INFO - ✅ 成功处理: 0 个公众号
2025-08-04 18:07:48,117 - INFO - ❌ 失败处理: 2 个公众号
2025-08-04 18:07:48,117 - INFO - 📄 总计文章: 0 篇
2025-08-04 18:07:48,117 - INFO - ================================================================================
2025-08-04 18:07:48,117 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 18:07:48,117 - INFO - ================================================================================
