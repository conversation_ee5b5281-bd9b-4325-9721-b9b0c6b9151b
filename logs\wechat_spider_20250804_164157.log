2025-08-04 16:41:57,904 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:41:57,904 - INFO - ================================================================================
2025-08-04 16:41:57,905 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:41:57,905 - INFO - ================================================================================
2025-08-04 16:41:57,905 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:41:57,975 - INFO - 找到有效目标 1: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 2: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 3: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 16:41:57,976 - INFO - 找到有效目标 4: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 16:41:57,977 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:41:57,977 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:41:57,977 - INFO - ============================================================
2025-08-04 16:41:57,977 - INFO - 📍 处理第 1/4 个公众号: 法润栖霞
2025-08-04 16:41:57,977 - INFO - ============================================================
2025-08-04 16:41:57,977 - INFO - [步骤 1/5] 为 '法润栖霞' 创建独立的 Cookie 抓取器...
2025-08-04 16:41:57,977 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:41:57,977 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:41:57,977 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:41:57,977 - INFO - === 开始重置网络状态 ===
2025-08-04 16:41:57,977 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:41:58,062 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:41:58,062 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:41:58,062 - INFO - 系统代理已成功关闭
2025-08-04 16:41:58,062 - INFO - ✅ 代理关闭操作
2025-08-04 16:41:58,062 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:41:59,645 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:41:59,646 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:41:59,647 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:41:59,647 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:41:59,649 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:42:00,148 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:42:00,149 - INFO - 🔄 Cookie抓取器进程已启动，PID: 27992
2025-08-04 16:42:00,149 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:42:03,150 - INFO - 等待代理服务启动...
2025-08-04 16:42:04,291 - INFO - 代理服务已启动并正常工作
2025-08-04 16:42:04,292 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 27992)
2025-08-04 16:42:04,292 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:42:04,292 - INFO - [步骤 2/5] 为 '法润栖霞' 启动 UI 自动化...
2025-08-04 16:42:04,292 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:42:04,293 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:42:04,293 - INFO - 正在查找微信主窗口...
2025-08-04 16:42:04,985 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:42:04,985 - INFO - 正在激活微信窗口...
2025-08-04 16:42:07,494 - INFO - 微信窗口已激活。
2025-08-04 16:42:07,495 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:42:14,186 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:42:14,187 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:42:17,520 - INFO - 正在查找聊天输入框...
2025-08-04 16:42:19,521 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:42:19,526 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:42:19,527 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:42:21,120 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&mid=2247548170&idx=1&sn=56ac1f201d459218024a9d219d0a8099&chksm=96dd984ca1aa115a7469db8d4f401c5f3119965621cfd35de2e1adadb482f938f458f9b7896d#rd
2025-08-04 16:42:23,410 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:42:23,646 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:42:24,454 - INFO - 链接已发送。
2025-08-04 16:42:27,455 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:42:29,546 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:42:30,285 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:42:33,286 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:42:33,286 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:42:33,287 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:42:33,292 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:42:36,000 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:42:37,501 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:42:44,847 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:42:44,848 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:42:44,848 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:42:44,849 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:42:44,849 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:44,849 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:42:44,850 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:42:44,850 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:44,850 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:42:44,850 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:42:44,850 - INFO - [步骤 3/5] 等待 '法润栖霞' 的 Cookie 数据...
2025-08-04 16:42:44,850 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:42:45,851 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:42:45,853 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:42:45,853 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:42:45,854 - INFO - [步骤 4/5] 停止 '法润栖霞' 的 Cookie 抓取器...
2025-08-04 16:42:45,854 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:42:45,855 - INFO - 正在停止Cookie抓取器 (PID: 27992)...
2025-08-04 16:42:45,856 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:42:45,856 - INFO - 正在验证并清理代理设置...
2025-08-04 16:42:45,857 - INFO - === 开始重置网络状态 ===
2025-08-04 16:42:45,857 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:42:45,952 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:42:45,952 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:42:45,952 - INFO - 系统代理已成功关闭
2025-08-04 16:42:45,952 - INFO - ✅ 代理关闭操作
2025-08-04 16:42:45,952 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:42:49,569 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:42:49,570 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:42:49,570 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:42:51,234 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:42:51,234 - INFO - ✅ 网络连接验证正常
2025-08-04 16:42:54,236 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:42:54,236 - INFO - [步骤 5/5] 开始爬取 '法润栖霞' 的文章...
2025-08-04 16:43:14,810 - INFO - ✅ 公众号 '法润栖霞' 爬取完成！获取 2 篇文章
2025-08-04 16:43:14,810 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_法润栖霞_20250804_164314.xlsx
2025-08-04 16:43:14,810 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:43:29,811 - INFO - ============================================================
2025-08-04 16:43:29,812 - INFO - 📍 处理第 2/4 个公众号: 雨花司法
2025-08-04 16:43:29,812 - INFO - ============================================================
2025-08-04 16:43:29,813 - INFO - [步骤 1/5] 为 '雨花司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:43:29,814 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:43:29,814 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:43:29,815 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:43:29,816 - INFO - === 开始重置网络状态 ===
2025-08-04 16:43:29,817 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:43:29,922 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:43:29,922 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:43:29,922 - INFO - 系统代理已成功关闭
2025-08-04 16:43:29,923 - INFO - ✅ 代理关闭操作
2025-08-04 16:43:29,923 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:43:31,137 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:43:31,138 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:43:31,138 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:43:31,138 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:43:31,139 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:43:31,645 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:43:31,646 - INFO - 🔄 Cookie抓取器进程已启动，PID: 18596
2025-08-04 16:43:31,646 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:43:34,646 - INFO - 等待代理服务启动...
2025-08-04 16:43:35,507 - INFO - 代理服务已启动并正常工作
2025-08-04 16:43:35,507 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 18596)
2025-08-04 16:43:35,508 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:43:35,508 - INFO - [步骤 2/5] 为 '雨花司法' 启动 UI 自动化...
2025-08-04 16:43:35,509 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:43:35,509 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:43:35,509 - INFO - 正在查找微信主窗口...
2025-08-04 16:43:35,744 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:43:35,744 - INFO - 正在激活微信窗口...
2025-08-04 16:43:38,262 - INFO - 微信窗口已激活。
2025-08-04 16:43:38,263 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:43:45,089 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:43:45,089 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:43:48,420 - INFO - 正在查找聊天输入框...
2025-08-04 16:43:50,421 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:43:50,428 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:43:50,428 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:43:52,005 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&mid=2247544684&idx=2&sn=ee23948433c816bc0616572d322a1737&chksm=eba92461dcdead77bee6ed2a6f90ee7824edab22aab2d8c2df6e984090d4cd4eb5c2173b662d#rd
2025-08-04 16:43:54,295 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:43:54,538 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:43:55,354 - INFO - 链接已发送。
2025-08-04 16:43:58,355 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:44:00,451 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:44:01,185 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:44:04,186 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:44:04,186 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:44:04,187 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:44:04,197 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:44:06,903 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:44:08,405 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:44:15,756 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:44:15,756 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:44:15,756 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:44:15,757 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:44:15,757 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:15,757 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:44:15,757 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:44:15,757 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:15,757 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:44:15,757 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:44:15,757 - INFO - [步骤 3/5] 等待 '雨花司法' 的 Cookie 数据...
2025-08-04 16:44:15,758 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:44:16,758 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:44:16,759 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:44:16,759 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:44:16,760 - INFO - [步骤 4/5] 停止 '雨花司法' 的 Cookie 抓取器...
2025-08-04 16:44:16,760 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:44:16,760 - INFO - 正在停止Cookie抓取器 (PID: 18596)...
2025-08-04 16:44:16,762 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:44:16,763 - INFO - 正在验证并清理代理设置...
2025-08-04 16:44:16,763 - INFO - === 开始重置网络状态 ===
2025-08-04 16:44:16,763 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:44:16,868 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:44:16,868 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:44:16,869 - INFO - 系统代理已成功关闭
2025-08-04 16:44:16,869 - INFO - ✅ 代理关闭操作
2025-08-04 16:44:16,869 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:44:17,837 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:44:17,837 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:44:17,838 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:44:18,774 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:44:18,774 - INFO - ✅ 网络连接验证正常
2025-08-04 16:44:21,775 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:44:21,776 - INFO - [步骤 5/5] 开始爬取 '雨花司法' 的文章...
2025-08-04 16:45:54,339 - INFO - ✅ 公众号 '雨花司法' 爬取完成！获取 9 篇文章
2025-08-04 16:45:54,339 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_雨花司法_20250804_164554.xlsx
2025-08-04 16:45:54,340 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:46:09,340 - INFO - ============================================================
2025-08-04 16:46:09,340 - INFO - 📍 处理第 3/4 个公众号: 江宁司法
2025-08-04 16:46:09,341 - INFO - ============================================================
2025-08-04 16:46:09,341 - INFO - [步骤 1/5] 为 '江宁司法' 创建独立的 Cookie 抓取器...
2025-08-04 16:46:09,342 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:46:09,343 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:46:09,343 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:46:09,343 - INFO - === 开始重置网络状态 ===
2025-08-04 16:46:09,343 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:46:09,444 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:46:09,444 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:46:09,444 - INFO - 系统代理已成功关闭
2025-08-04 16:46:09,444 - INFO - ✅ 代理关闭操作
2025-08-04 16:46:09,444 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:46:12,487 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:46:12,487 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:46:12,488 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:46:12,488 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:46:12,489 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:46:12,984 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:46:12,985 - INFO - 🔄 Cookie抓取器进程已启动，PID: 15884
2025-08-04 16:46:12,985 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:46:15,986 - INFO - 等待代理服务启动...
2025-08-04 16:46:17,467 - INFO - 代理服务已启动并正常工作
2025-08-04 16:46:17,468 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 15884)
2025-08-04 16:46:17,469 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:46:17,469 - INFO - [步骤 2/5] 为 '江宁司法' 启动 UI 自动化...
2025-08-04 16:46:17,470 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:46:17,470 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:46:17,471 - INFO - 正在查找微信主窗口...
2025-08-04 16:46:17,943 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:46:17,944 - INFO - 正在激活微信窗口...
2025-08-04 16:46:20,458 - INFO - 微信窗口已激活。
2025-08-04 16:46:20,459 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:46:27,151 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:46:27,152 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:46:30,478 - INFO - 正在查找聊天输入框...
2025-08-04 16:46:32,478 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:46:32,488 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:46:32,489 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:46:34,058 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&mid=2652140303&idx=1&sn=74a10900f46e96be6567975b1d1ccd59&chksm=bda865f78adfece121715d85d10271b02fb19ce45ba1cfd5135e99209e0775ba3a82914ee28b#rd
2025-08-04 16:46:36,347 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:46:36,590 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:46:37,404 - INFO - 链接已发送。
2025-08-04 16:46:40,405 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:46:42,497 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:46:43,221 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:46:46,222 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:46:46,222 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:46:46,222 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:46:46,228 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:46:48,936 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:46:50,437 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:46:57,791 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:46:57,791 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:46:57,791 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:46:57,791 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:46:57,792 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:57,792 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:46:57,792 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:46:57,792 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:57,792 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:46:57,792 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:46:57,792 - INFO - [步骤 3/5] 等待 '江宁司法' 的 Cookie 数据...
2025-08-04 16:46:57,792 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:46:58,793 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:46:58,794 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:46:58,794 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:46:58,795 - INFO - [步骤 4/5] 停止 '江宁司法' 的 Cookie 抓取器...
2025-08-04 16:46:58,795 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:46:58,795 - INFO - 正在停止Cookie抓取器 (PID: 15884)...
2025-08-04 16:46:58,798 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:46:58,798 - INFO - 正在验证并清理代理设置...
2025-08-04 16:46:58,799 - INFO - === 开始重置网络状态 ===
2025-08-04 16:46:58,799 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:46:58,907 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:46:58,907 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:46:58,907 - INFO - 系统代理已成功关闭
2025-08-04 16:46:58,907 - INFO - ✅ 代理关闭操作
2025-08-04 16:46:58,907 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:47:01,689 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:47:01,689 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:47:01,690 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:47:02,612 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:47:02,613 - INFO - ✅ 网络连接验证正常
2025-08-04 16:47:05,614 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:47:05,615 - INFO - [步骤 5/5] 开始爬取 '江宁司法' 的文章...
2025-08-04 16:47:49,862 - INFO - ✅ 公众号 '江宁司法' 爬取完成！获取 5 篇文章
2025-08-04 16:47:49,862 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_江宁司法_20250804_164749.xlsx
2025-08-04 16:47:49,862 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:48:04,862 - INFO - ============================================================
2025-08-04 16:48:04,863 - INFO - 📍 处理第 4/4 个公众号: 浦口普法
2025-08-04 16:48:04,864 - INFO - ============================================================
2025-08-04 16:48:04,865 - INFO - [步骤 1/5] 为 '浦口普法' 创建独立的 Cookie 抓取器...
2025-08-04 16:48:04,866 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:48:04,867 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:48:04,868 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:48:04,868 - INFO - === 开始重置网络状态 ===
2025-08-04 16:48:04,869 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:48:04,975 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:48:04,976 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:48:04,976 - INFO - 系统代理已成功关闭
2025-08-04 16:48:04,976 - INFO - ✅ 代理关闭操作
2025-08-04 16:48:04,976 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:48:06,856 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:06,856 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:48:06,857 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:48:06,857 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:48:06,858 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:48:07,356 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:48:07,357 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33152
2025-08-04 16:48:07,357 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:48:10,357 - INFO - 等待代理服务启动...
2025-08-04 16:48:12,002 - INFO - 代理服务已启动并正常工作
2025-08-04 16:48:12,003 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33152)
2025-08-04 16:48:12,003 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:48:12,003 - INFO - [步骤 2/5] 为 '浦口普法' 启动 UI 自动化...
2025-08-04 16:48:12,003 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:48:12,003 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:48:12,003 - INFO - 正在查找微信主窗口...
2025-08-04 16:48:12,224 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:48:12,224 - INFO - 正在激活微信窗口...
2025-08-04 16:48:14,741 - INFO - 微信窗口已激活。
2025-08-04 16:48:14,741 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:48:21,436 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:48:21,437 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:48:24,771 - INFO - 正在查找聊天输入框...
2025-08-04 16:48:26,772 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:48:26,780 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:48:26,781 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:48:28,347 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&mid=2452706749&idx=1&sn=b59ee1711a93713adc69fbbdc9b557c6&chksm=8cef28c6bb98a1d09cc437733de4ca183767c6e852a2fba8c0a86e699900e8cda2f1280fbac5#rd
2025-08-04 16:48:30,639 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:48:30,885 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:48:31,684 - INFO - 链接已发送。
2025-08-04 16:48:34,686 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:48:36,777 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:48:37,504 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:48:40,505 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:48:40,506 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:48:40,506 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:48:40,514 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:48:43,220 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:48:44,720 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:48:52,090 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:48:52,091 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:48:52,091 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:48:52,091 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:48:52,091 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:52,091 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:48:52,091 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:48:52,091 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:52,092 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:48:52,092 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:48:52,092 - INFO - [步骤 3/5] 等待 '浦口普法' 的 Cookie 数据...
2025-08-04 16:48:52,092 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:48:53,092 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:48:53,094 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:48:53,094 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:48:53,094 - INFO - [步骤 4/5] 停止 '浦口普法' 的 Cookie 抓取器...
2025-08-04 16:48:53,095 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:48:53,095 - INFO - 正在停止Cookie抓取器 (PID: 33152)...
2025-08-04 16:48:53,099 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:48:53,100 - INFO - 正在验证并清理代理设置...
2025-08-04 16:48:53,100 - INFO - === 开始重置网络状态 ===
2025-08-04 16:48:53,100 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:48:53,198 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:48:53,198 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:48:53,198 - INFO - 系统代理已成功关闭
2025-08-04 16:48:53,198 - INFO - ✅ 代理关闭操作
2025-08-04 16:48:53,198 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:48:54,116 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:54,116 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:48:54,117 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:48:57,200 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:48:57,201 - INFO - ✅ 网络连接验证正常
2025-08-04 16:49:00,202 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:49:00,203 - INFO - [步骤 5/5] 开始爬取 '浦口普法' 的文章...
2025-08-04 16:49:22,100 - INFO - ✅ 公众号 '浦口普法' 爬取完成！获取 2 篇文章
2025-08-04 16:49:22,100 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_浦口普法_20250804_164922.xlsx
2025-08-04 16:49:22,100 - INFO - ================================================================================
2025-08-04 16:49:22,100 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 16:49:22,100 - INFO - ================================================================================
2025-08-04 16:49:22,100 - INFO - ✅ 成功处理: 4 个公众号
2025-08-04 16:49:22,100 - INFO - ❌ 失败处理: 0 个公众号
2025-08-04 16:49:22,100 - INFO - 📄 总计文章: 18 篇
2025-08-04 16:49:22,128 - INFO - 🎉 汇总数据已保存到:
2025-08-04 16:49:22,129 - INFO - 📊 Excel: ./data/readnum_batch/readnum_summary_20250804_164922.xlsx
2025-08-04 16:49:22,129 - INFO - 💾 JSON: ./data/readnum_batch/readnum_summary_20250804_164922.json
2025-08-04 16:49:22,129 - INFO - ================================================================================
2025-08-04 16:49:22,129 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 16:49:22,129 - INFO - ================================================================================
