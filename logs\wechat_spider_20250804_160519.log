2025-08-04 16:05:19,503 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 16:05:19,503 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:05:19,503 - INFO - ================================================================================
2025-08-04 16:05:19,503 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 16:05:19,504 - INFO - ================================================================================
2025-08-04 16:05:19,504 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 16:05:19,577 - INFO - 找到有效目标 1: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-04 16:05:19,577 - INFO - 找到有效目标 2: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-04 16:05:19,578 - INFO - 找到有效目标 3: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-04 16:05:19,578 - INFO - 找到有效目标 4: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-04 16:05:19,578 - INFO - 共找到 4 个有效的公众号目标
2025-08-04 16:05:19,578 - INFO - 📋 共找到 4 个公众号，开始逐个处理...
2025-08-04 16:05:19,579 - INFO - ============================================================
2025-08-04 16:05:19,579 - INFO - 📍 处理第 1/4 个公众号: 南京市城管局
2025-08-04 16:05:19,579 - INFO - ============================================================
2025-08-04 16:05:19,579 - INFO - [步骤 1/5] 为 '南京市城管局' 启动 Cookie 抓取器...
2025-08-04 16:05:19,579 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:05:19,579 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:05:19,579 - INFO - === 开始重置网络状态 ===
2025-08-04 16:05:19,579 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:05:19,661 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:05:19,661 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:05:19,661 - INFO - 系统代理已成功关闭
2025-08-04 16:05:19,661 - INFO - ✅ 代理关闭操作
2025-08-04 16:05:19,661 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:05:23,677 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:05:23,678 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:05:23,678 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:05:23,679 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:05:23,680 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:05:24,187 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:05:24,188 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32276
2025-08-04 16:05:24,188 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:05:27,189 - INFO - 等待代理服务启动...
2025-08-04 16:05:32,662 - INFO - 代理服务已启动并正常工作
2025-08-04 16:05:32,663 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32276)
2025-08-04 16:05:32,664 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:05:32,664 - INFO - [步骤 2/5] 为 '南京市城管局' 启动 UI 自动化...
2025-08-04 16:05:32,665 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:05:32,666 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:05:32,666 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:05:32,667 - INFO - 正在查找微信主窗口...
2025-08-04 16:05:33,461 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:05:33,462 - INFO - 正在激活微信窗口...
2025-08-04 16:05:35,974 - INFO - 微信窗口已激活。
2025-08-04 16:05:35,974 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:05:42,720 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:05:42,721 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:05:46,047 - INFO - 正在查找聊天输入框...
2025-08-04 16:05:48,048 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:05:48,061 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:05:48,061 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:05:49,655 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&mid=2247564011&idx=1&sn=e94d40f840abcb2aedaf3f93eafa8372&chksm=eb4b7732dc3cfe24e9d2c8c4075e9d8b23edd57f84c7e6ff8510365992e761a0d21e2988bc70#rd
2025-08-04 16:05:51,941 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:05:52,152 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:05:52,953 - INFO - 链接已发送。
2025-08-04 16:05:55,955 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:05:58,051 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:05:58,783 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:06:01,784 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:06:01,785 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:06:01,785 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:06:01,794 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:06:04,500 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:06:06,002 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:06:13,996 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:06:13,996 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:06:13,996 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:06:13,996 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:06:13,997 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:13,997 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:06:13,997 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:06:13,998 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:13,998 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:06:13,998 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:06:13,998 - INFO - [步骤 3/5] 等待 '南京市城管局' 的 Cookie 数据...
2025-08-04 16:06:13,999 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:06:14,999 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:06:15,000 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:06:15,001 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:06:15,001 - INFO - [步骤 4/5] 停止 '南京市城管局' 的 Cookie 抓取器...
2025-08-04 16:06:15,001 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:06:15,002 - INFO - 正在停止Cookie抓取器 (PID: 32276)...
2025-08-04 16:06:15,004 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:06:15,004 - INFO - 正在验证并清理代理设置...
2025-08-04 16:06:15,004 - INFO - === 开始重置网络状态 ===
2025-08-04 16:06:15,005 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:06:15,096 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:06:15,097 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:06:15,097 - INFO - 系统代理已成功关闭
2025-08-04 16:06:15,097 - INFO - ✅ 代理关闭操作
2025-08-04 16:06:15,097 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:06:16,587 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:16,587 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:06:16,587 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:06:18,804 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:18,805 - INFO - ✅ 网络连接验证正常
2025-08-04 16:06:21,806 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:06:21,807 - INFO - [步骤 5/5] 开始爬取 '南京市城管局' 的文章...
2025-08-04 16:06:21,938 - WARNING - ⚠️ 公众号 '南京市城管局' 未获取到任何文章数据
2025-08-04 16:06:21,939 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 16:06:36,939 - INFO - ============================================================
2025-08-04 16:06:36,940 - INFO - 📍 处理第 2/4 个公众号: 南京美丽乡村
2025-08-04 16:06:36,941 - INFO - ============================================================
2025-08-04 16:06:36,941 - INFO - [步骤 1/5] 为 '南京美丽乡村' 启动 Cookie 抓取器...
2025-08-04 16:06:36,942 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 16:06:36,942 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 16:06:36,943 - INFO - === 开始重置网络状态 ===
2025-08-04 16:06:36,943 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:06:37,039 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:06:37,039 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:06:37,040 - INFO - 系统代理已成功关闭
2025-08-04 16:06:37,040 - INFO - ✅ 代理关闭操作
2025-08-04 16:06:37,040 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:06:39,741 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:06:39,742 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:06:39,742 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 16:06:39,743 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 16:06:39,744 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 16:06:40,252 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 16:06:40,253 - INFO - 🔄 Cookie抓取器进程已启动，PID: 21744
2025-08-04 16:06:40,253 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 16:06:43,254 - INFO - 等待代理服务启动...
2025-08-04 16:06:43,832 - INFO - 代理服务已启动并正常工作
2025-08-04 16:06:43,833 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 21744)
2025-08-04 16:06:43,834 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 16:06:43,834 - INFO - [步骤 2/5] 为 '南京美丽乡村' 启动 UI 自动化...
2025-08-04 16:06:43,835 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 16:06:43,836 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 16:06:43,836 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 16:06:43,836 - INFO - 正在查找微信主窗口...
2025-08-04 16:06:44,090 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 16:06:44,090 - INFO - 正在激活微信窗口...
2025-08-04 16:06:46,608 - INFO - 微信窗口已激活。
2025-08-04 16:06:46,609 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 16:06:53,222 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 16:06:53,223 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 16:06:56,553 - INFO - 正在查找聊天输入框...
2025-08-04 16:06:58,554 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 16:06:58,565 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 16:06:58,565 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 16:07:00,150 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&mid=2247521900&idx=1&sn=7718b37c481a1948c6ff820192cf3207&chksm=97617896a016f180f83bc89bd8f8ae968e92b0b67273d0c0f71340d2a34bcb92436000b6e147#rd
2025-08-04 16:07:02,438 - INFO - 链接已粘贴，正在发送...
2025-08-04 16:07:02,638 - INFO - 找到发送按钮，点击发送...
2025-08-04 16:07:03,453 - INFO - 链接已发送。
2025-08-04 16:07:06,454 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 16:07:08,543 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 16:07:09,269 - INFO - ✅ 成功点击最新链接。
2025-08-04 16:07:12,270 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 16:07:12,270 - INFO - 正在查找微信浏览器窗口...
2025-08-04 16:07:12,270 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 16:07:12,277 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 16:07:14,983 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 16:07:16,484 - INFO - 正在检测SSL证书错误页面...
2025-08-04 16:07:24,491 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 16:07:24,492 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 16:07:24,492 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 16:07:24,492 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 16:07:24,492 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:24,492 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 16:07:24,492 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 16:07:24,492 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:24,492 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 16:07:24,493 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 16:07:24,493 - INFO - [步骤 3/5] 等待 '南京美丽乡村' 的 Cookie 数据...
2025-08-04 16:07:24,493 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 16:07:25,493 - INFO - 检测到Cookie文件已生成。
2025-08-04 16:07:25,495 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 16:07:25,495 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 16:07:25,495 - INFO - [步骤 4/5] 停止 '南京美丽乡村' 的 Cookie 抓取器...
2025-08-04 16:07:25,496 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 16:07:25,496 - INFO - 正在停止Cookie抓取器 (PID: 21744)...
2025-08-04 16:07:25,498 - INFO - Cookie抓取器已成功终止。
2025-08-04 16:07:25,499 - INFO - 正在验证并清理代理设置...
2025-08-04 16:07:25,499 - INFO - === 开始重置网络状态 ===
2025-08-04 16:07:25,499 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 16:07:25,601 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 16:07:25,602 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 16:07:25,602 - INFO - 系统代理已成功关闭
2025-08-04 16:07:25,602 - INFO - ✅ 代理关闭操作
2025-08-04 16:07:25,603 - INFO - 🔗 正在验证网络连接...
2025-08-04 16:07:27,192 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:07:27,192 - INFO - ✅ 网络状态重置验证完成
2025-08-04 16:07:27,192 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 16:07:28,338 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 16:07:28,339 - INFO - ✅ 网络连接验证正常
2025-08-04 16:07:31,339 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 16:07:31,340 - INFO - [步骤 5/5] 开始爬取 '南京美丽乡村' 的文章...
2025-08-04 16:07:31,462 - WARNING - ⚠️ 公众号 '南京美丽乡村' 未获取到任何文章数据
2025-08-04 16:07:31,463 - INFO - ⏳ 公众号间延迟 15 秒...
