2025-08-04 17:34:34,994 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:34:34,994 - INFO - ================================================================================
2025-08-04 17:34:34,994 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:34:34,994 - INFO - ================================================================================
2025-08-04 17:34:34,995 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:34:35,066 - INFO - 找到有效目标 1: 南京市交通集团 - http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&m...
2025-08-04 17:34:35,066 - INFO - 找到有效目标 2: 六合智慧普法 - http://mp.weixin.qq.com/s?__biz=MzIwMjIwNjg1NA==&m...
2025-08-04 17:34:35,067 - INFO - 共找到 2 个有效的公众号目标
2025-08-04 17:34:35,067 - INFO - 📋 共找到 2 个公众号，开始逐个处理...
2025-08-04 17:34:35,067 - INFO - ============================================================
2025-08-04 17:34:35,067 - INFO - 📍 处理第 1/2 个公众号: 南京市交通集团
2025-08-04 17:34:35,067 - INFO - ============================================================
2025-08-04 17:34:35,067 - INFO - [步骤 1/5] 为 '南京市交通集团' 创建独立的 Cookie 抓取器...
2025-08-04 17:34:35,067 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:34:35,067 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:34:35,067 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:34:35,067 - INFO - === 开始重置网络状态 ===
2025-08-04 17:34:35,068 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:34:35,153 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:34:35,153 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:34:35,153 - INFO - 系统代理已成功关闭
2025-08-04 17:34:35,154 - INFO - ✅ 代理关闭操作
2025-08-04 17:34:35,154 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:34:37,433 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:34:37,434 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:34:37,434 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:34:37,435 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:34:37,436 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:34:37,961 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:34:37,962 - INFO - 🔄 Cookie抓取器进程已启动，PID: 18748
2025-08-04 17:34:37,962 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:34:40,963 - INFO - 等待代理服务启动...
2025-08-04 17:34:41,927 - INFO - 代理服务已启动并正常工作
2025-08-04 17:34:41,928 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 18748)
2025-08-04 17:34:41,928 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:34:41,929 - INFO - [步骤 2/5] 为 '南京市交通集团' 启动 UI 自动化...
2025-08-04 17:34:41,929 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:34:41,929 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:34:41,929 - INFO - 正在查找微信主窗口...
2025-08-04 17:34:44,567 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:34:44,567 - INFO - 正在激活微信窗口...
2025-08-04 17:34:47,081 - INFO - 微信窗口已激活。
2025-08-04 17:34:47,082 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:34:53,754 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:34:53,755 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:34:57,090 - INFO - 正在查找聊天输入框...
2025-08-04 17:34:59,091 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:34:59,101 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:34:59,102 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:35:00,710 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&mid=2247533124&idx=1&sn=bb5defd4afd5338b96e11e9328371330&chksm=eaacbf85dddb36930b5188ca9e5c6285ee8c42916252d9d14c15bf8699ade72d594e74bc5122#rd
2025-08-04 17:35:03,003 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:35:03,239 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:35:04,055 - INFO - 链接已发送。
2025-08-04 17:35:07,056 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:35:09,148 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:35:09,873 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:35:12,874 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:35:12,875 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:35:12,875 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:35:12,883 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:35:15,589 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:35:17,090 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:35:24,332 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:35:24,333 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:35:24,333 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:35:24,333 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:35:24,333 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:35:24,333 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:35:24,333 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:35:24,334 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:35:24,334 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:35:24,334 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:35:24,334 - INFO - [步骤 3/5] 等待 '南京市交通集团' 的 Cookie 数据...
2025-08-04 17:35:24,334 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:35:25,335 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:35:25,336 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:35:25,337 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 17:35:25,450 - WARNING - ⚠️ Cookie无效（第1次尝试）
2025-08-04 17:35:25,450 - INFO - 🔄 Cookie无效，准备重新抓取...
2025-08-04 17:35:25,451 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:35:26,452 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:35:26,453 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:35:26,454 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 17:35:26,569 - WARNING - ⚠️ Cookie无效（第2次尝试）
2025-08-04 17:35:26,569 - ERROR - ❌ 多次尝试后Cookie仍然无效
2025-08-04 17:35:26,570 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:35:26,570 - INFO - 正在停止Cookie抓取器 (PID: 18748)...
2025-08-04 17:35:26,572 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:35:26,572 - INFO - 正在验证并清理代理设置...
2025-08-04 17:35:26,573 - INFO - === 开始重置网络状态 ===
2025-08-04 17:35:26,573 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:35:26,672 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:35:26,673 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:35:26,673 - INFO - 系统代理已成功关闭
2025-08-04 17:35:26,673 - INFO - ✅ 代理关闭操作
2025-08-04 17:35:26,673 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:35:27,746 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:35:27,747 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:35:27,747 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:35:30,001 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:35:30,002 - INFO - ✅ 网络连接验证正常
2025-08-04 17:35:30,002 - INFO - ✅ 成功获取并验证了有效的 Cookie。
2025-08-04 17:35:30,003 - INFO - [步骤 4/5] 停止 '南京市交通集团' 的 Cookie 抓取器...
2025-08-04 17:35:30,003 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:35:30,003 - INFO - Cookie抓取器未在运行或已停止。
2025-08-04 17:35:30,003 - INFO - 正在验证并清理代理设置...
2025-08-04 17:35:30,003 - INFO - === 开始重置网络状态 ===
2025-08-04 17:35:30,004 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:35:30,109 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:35:30,110 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:35:30,110 - INFO - 系统代理已成功关闭
2025-08-04 17:35:30,110 - INFO - ✅ 代理关闭操作
2025-08-04 17:35:30,110 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:35:31,757 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:35:31,757 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:35:31,758 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:35:34,703 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:35:34,704 - INFO - ✅ 网络连接验证正常
2025-08-04 17:35:37,704 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:35:37,705 - INFO - [步骤 5/5] 开始爬取 '南京市交通集团' 的文章...
2025-08-04 17:35:37,815 - WARNING - ⚠️ 公众号 '南京市交通集团' 未获取到任何文章数据
2025-08-04 17:35:37,815 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-04 17:35:52,816 - INFO - ============================================================
2025-08-04 17:35:52,817 - INFO - 📍 处理第 2/2 个公众号: 六合智慧普法
2025-08-04 17:35:52,817 - INFO - ============================================================
2025-08-04 17:35:52,818 - INFO - [步骤 1/5] 为 '六合智慧普法' 创建独立的 Cookie 抓取器...
2025-08-04 17:35:52,818 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:35:52,819 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:35:52,819 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:35:52,819 - INFO - === 开始重置网络状态 ===
2025-08-04 17:35:52,820 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:35:52,912 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:35:52,912 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:35:52,912 - INFO - 系统代理已成功关闭
2025-08-04 17:35:52,912 - INFO - ✅ 代理关闭操作
2025-08-04 17:35:52,912 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:35:57,788 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:35:57,789 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:35:57,789 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:35:57,790 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:35:57,790 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:35:58,317 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:35:58,318 - INFO - 🔄 Cookie抓取器进程已启动，PID: 14788
2025-08-04 17:35:58,318 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:36:01,319 - INFO - 等待代理服务启动...
2025-08-04 17:36:03,220 - INFO - 代理服务已启动并正常工作
2025-08-04 17:36:03,221 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 14788)
2025-08-04 17:36:03,221 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:36:03,222 - INFO - [步骤 2/5] 为 '六合智慧普法' 启动 UI 自动化...
2025-08-04 17:36:03,223 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:36:03,224 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:36:03,224 - INFO - 正在查找微信主窗口...
2025-08-04 17:36:03,528 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:36:03,528 - INFO - 正在激活微信窗口...
2025-08-04 17:36:06,041 - INFO - 微信窗口已激活。
2025-08-04 17:36:06,042 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:36:12,790 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:36:12,790 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:36:16,122 - INFO - 正在查找聊天输入框...
2025-08-04 17:36:18,123 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:36:18,136 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:36:18,136 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:36:19,726 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzIwMjIwNjg1NA==&mid=2653000656&idx=1&sn=b4dc76b3a98d72e1e3a9ea0b887c3f55&chksm=8d370839ba40812f348ef5b205f702934f9b438105efb4941c8e061a38edf9e42ea9f271267e#rd
2025-08-04 17:36:22,016 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:36:22,256 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:36:23,053 - INFO - 链接已发送。
2025-08-04 17:36:26,054 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:36:28,145 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:36:28,873 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:36:31,874 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:36:31,875 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:36:31,875 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:36:31,883 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:36:34,588 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:36:36,090 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:36:43,476 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:36:43,477 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:36:43,477 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:36:43,477 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:36:43,477 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:36:43,477 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:36:43,477 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:36:43,477 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:36:43,478 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:36:43,478 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:36:43,478 - INFO - [步骤 3/5] 等待 '六合智慧普法' 的 Cookie 数据...
2025-08-04 17:36:43,478 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:36:44,478 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:36:44,479 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:36:44,480 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 17:36:44,603 - WARNING - ⚠️ Cookie无效（第1次尝试）
2025-08-04 17:36:44,603 - INFO - 🔄 Cookie无效，准备重新抓取...
2025-08-04 17:36:44,603 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:36:45,604 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:36:45,606 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:36:45,606 - INFO - 🔍 立即验证Cookie有效性...
2025-08-04 17:36:45,754 - WARNING - ⚠️ Cookie无效（第2次尝试）
2025-08-04 17:36:45,755 - ERROR - ❌ 多次尝试后Cookie仍然无效
2025-08-04 17:36:45,755 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:36:45,756 - INFO - 正在停止Cookie抓取器 (PID: 14788)...
2025-08-04 17:36:45,758 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:36:45,759 - INFO - 正在验证并清理代理设置...
2025-08-04 17:36:45,759 - INFO - === 开始重置网络状态 ===
2025-08-04 17:36:45,759 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:36:45,855 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:36:45,855 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:36:45,855 - INFO - 系统代理已成功关闭
2025-08-04 17:36:45,855 - INFO - ✅ 代理关闭操作
2025-08-04 17:36:45,855 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:36:47,802 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:36:47,802 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:36:47,802 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:36:48,770 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:36:48,770 - INFO - ✅ 网络连接验证正常
2025-08-04 17:36:48,771 - INFO - ✅ 成功获取并验证了有效的 Cookie。
2025-08-04 17:36:48,771 - INFO - [步骤 4/5] 停止 '六合智慧普法' 的 Cookie 抓取器...
2025-08-04 17:36:48,771 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:36:48,772 - INFO - Cookie抓取器未在运行或已停止。
2025-08-04 17:36:48,772 - INFO - 正在验证并清理代理设置...
2025-08-04 17:36:48,772 - INFO - === 开始重置网络状态 ===
2025-08-04 17:36:48,772 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:36:48,865 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:36:48,865 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:36:48,865 - INFO - 系统代理已成功关闭
2025-08-04 17:36:48,865 - INFO - ✅ 代理关闭操作
2025-08-04 17:36:48,865 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:36:50,094 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:36:50,095 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:36:50,095 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:36:52,408 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:36:52,409 - INFO - ✅ 网络连接验证正常
2025-08-04 17:36:55,409 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:36:55,410 - INFO - [步骤 5/5] 开始爬取 '六合智慧普法' 的文章...
2025-08-04 17:36:55,532 - WARNING - ⚠️ 公众号 '六合智慧普法' 未获取到任何文章数据
2025-08-04 17:36:55,533 - INFO - ================================================================================
2025-08-04 17:36:55,533 - INFO - 📊 多公众号爬取汇总结果
2025-08-04 17:36:55,534 - INFO - ================================================================================
2025-08-04 17:36:55,534 - INFO - ✅ 成功处理: 0 个公众号
2025-08-04 17:36:55,535 - INFO - ❌ 失败处理: 4 个公众号
2025-08-04 17:36:55,535 - INFO - 📄 总计文章: 0 篇
2025-08-04 17:36:55,536 - INFO - ================================================================================
2025-08-04 17:36:55,536 - INFO - ✅ 多公众号全新自动化流程执行完毕 ✅
2025-08-04 17:36:55,536 - INFO - ================================================================================
