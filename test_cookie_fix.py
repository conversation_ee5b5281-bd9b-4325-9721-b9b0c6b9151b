#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cookie文件删除问题的修复
"""
import os
import time
from read_cookie import Read<PERSON><PERSON><PERSON>

def create_test_cookie_file():
    """创建一个测试用的Cookie文件"""
    test_content = """============================================================
time: 2025-08-04 16:14:31
allurl: https://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&mid=2247688440&idx=1&sn=1b728e419d1fcbadc2c2e132bd18e976&chksm=e9b27d1ddec5f40b58ab6491fe0f56b49c4fce971a3a7f633241cf66c0317edfa5364d007f61&key=test_key
Cookies: wxuin=2635502856; appmsg_token=1333_M6XOZpMJQK3w914pH_UUb_tbYwjF5PET7k-nS8j8f4hVS2ZxVoROs2r3i9tEc8LQwpIhcqJ7GzBLsDXr; pass_ticket=test_ticket; wap_sid2=test_sid
Headers:
  x-wechat-key: test_key
  x-wechat-uin: MjYzNTUwMjg1Ng%3D%3D
  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

"""
    with open('wechat_keys.txt', 'w', encoding='utf-8') as f:
        f.write(test_content)
    print("✅ 创建了测试Cookie文件")

def test_cookie_preservation():
    """测试Cookie文件是否被意外删除"""
    print("🧪 测试Cookie文件保护机制")
    print("=" * 50)
    
    # 1. 创建测试Cookie文件
    create_test_cookie_file()
    
    # 2. 测试只读取Cookie（不应该删除文件）
    print("\n📖 测试1: 只读取Cookie（应该保留文件）")
    if os.path.exists('wechat_keys.txt'):
        print("✅ Cookie文件存在")
        
        # 使用delete_existing_file=False创建ReadCookie实例
        cookie_reader = ReadCookie('wechat_keys.txt', delete_existing_file=False)
        result = cookie_reader.get_latest_cookies()
        
        if os.path.exists('wechat_keys.txt'):
            print("✅ Cookie文件仍然存在（正确行为）")
            if result:
                print(f"✅ 成功解析Cookie: biz={result['biz']}")
            else:
                print("❌ Cookie解析失败")
        else:
            print("❌ Cookie文件被意外删除（错误行为）")
    else:
        print("❌ 测试Cookie文件创建失败")
    
    # 3. 测试重新抓取Cookie（应该删除文件）
    print("\n🔄 测试2: 重新抓取Cookie（应该删除旧文件）")
    if os.path.exists('wechat_keys.txt'):
        print("✅ Cookie文件存在")
        
        # 使用默认参数创建ReadCookie实例（delete_existing_file=True）
        cookie_reader = ReadCookie('wechat_keys.txt')
        
        if not os.path.exists('wechat_keys.txt'):
            print("✅ 旧Cookie文件被正确删除（正确行为）")
        else:
            print("❌ 旧Cookie文件未被删除（可能的错误）")
    else:
        print("⚠️ 没有Cookie文件可供测试删除")

def test_excel_auto_crawler_logic():
    """测试ExcelAutoCrawler的Cookie处理逻辑"""
    print("\n🤖 测试3: ExcelAutoCrawler Cookie处理")
    
    # 重新创建测试文件
    create_test_cookie_file()
    
    try:
        from excel_auto_crawler import ExcelAutoCrawler
        
        # 创建ExcelAutoCrawler实例（应该不删除现有Cookie文件）
        print("创建ExcelAutoCrawler实例...")
        crawler = ExcelAutoCrawler()
        
        if os.path.exists('wechat_keys.txt'):
            print("✅ ExcelAutoCrawler创建时保留了现有Cookie文件（正确行为）")
            
            # 测试获取现有Cookie
            result = crawler.cookie_reader.get_latest_cookies()
            if result:
                print(f"✅ 成功读取现有Cookie: biz={result['biz']}")
            else:
                print("❌ 无法读取现有Cookie")
        else:
            print("❌ ExcelAutoCrawler创建时删除了现有Cookie文件（错误行为）")
            
    except ImportError as e:
        print(f"⚠️ 无法导入ExcelAutoCrawler: {e}")
    except Exception as e:
        print(f"❌ 测试ExcelAutoCrawler时出错: {e}")

if __name__ == "__main__":
    test_cookie_preservation()
    test_excel_auto_crawler_logic()
    
    # 清理测试文件
    if os.path.exists('wechat_keys.txt'):
        os.remove('wechat_keys.txt')
        print("\n🧹 已清理测试文件")
    
    print("\n✅ 测试完成")
