2025-08-04 17:23:24,844 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 17:23:24,844 - INFO - ================================================================================
2025-08-04 17:23:24,844 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 17:23:24,844 - INFO - ================================================================================
2025-08-04 17:23:24,844 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 17:23:24,922 - INFO - 找到有效目标 1: 南京市残疾人联合会 - http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&m...
2025-08-04 17:23:24,923 - INFO - 共找到 1 个有效的公众号目标
2025-08-04 17:23:24,923 - INFO - 📋 共找到 1 个公众号，开始逐个处理...
2025-08-04 17:23:24,923 - INFO - ============================================================
2025-08-04 17:23:24,923 - INFO - 📍 处理第 1/1 个公众号: 南京市残疾人联合会
2025-08-04 17:23:24,924 - INFO - ============================================================
2025-08-04 17:23:24,924 - INFO - [步骤 1/5] 为 '南京市残疾人联合会' 创建独立的 Cookie 抓取器...
2025-08-04 17:23:24,924 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 17:23:24,924 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 17:23:24,924 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 17:23:24,924 - INFO - === 开始重置网络状态 ===
2025-08-04 17:23:24,925 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:23:25,029 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:23:25,029 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:23:25,029 - INFO - 系统代理已成功关闭
2025-08-04 17:23:25,029 - INFO - ✅ 代理关闭操作
2025-08-04 17:23:25,029 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:23:28,392 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:23:28,393 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:23:28,393 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 17:23:28,394 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 17:23:28,395 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 17:23:28,940 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 17:23:28,941 - INFO - 🔄 Cookie抓取器进程已启动，PID: 26464
2025-08-04 17:23:28,941 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 17:23:31,942 - INFO - 等待代理服务启动...
2025-08-04 17:23:35,060 - INFO - 代理服务已启动并正常工作
2025-08-04 17:23:35,060 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 26464)
2025-08-04 17:23:35,060 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 17:23:35,060 - INFO - [步骤 2/5] 为 '南京市残疾人联合会' 启动 UI 自动化...
2025-08-04 17:23:35,061 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 17:23:35,061 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 17:23:35,061 - INFO - 正在查找微信主窗口...
2025-08-04 17:23:37,406 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 17:23:37,406 - INFO - 正在激活微信窗口...
2025-08-04 17:23:39,915 - INFO - 微信窗口已激活。
2025-08-04 17:23:39,916 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 17:23:46,773 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 17:23:46,773 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 17:23:50,105 - INFO - 正在查找聊天输入框...
2025-08-04 17:23:52,106 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 17:23:52,119 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 17:23:52,119 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 17:23:53,706 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&mid=2247567892&idx=1&sn=9639c9464cea66b9caa7d7d316b094f9&chksm=fc7bec9ccb0c658a065a4fd8be47fd22591cc463e3972acc708e89186d370175a0c880db0455#rd
2025-08-04 17:23:55,994 - INFO - 链接已粘贴，正在发送...
2025-08-04 17:23:56,231 - INFO - 找到发送按钮，点击发送...
2025-08-04 17:23:57,038 - INFO - 链接已发送。
2025-08-04 17:24:00,040 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 17:24:02,128 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 17:24:02,856 - INFO - ✅ 成功点击最新链接。
2025-08-04 17:24:05,857 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-04 17:24:05,858 - INFO - 正在查找微信浏览器窗口...
2025-08-04 17:24:05,858 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 17:24:05,871 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 17:24:08,577 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 17:24:10,079 - INFO - 正在检测SSL证书错误页面...
2025-08-04 17:24:17,493 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-04 17:24:17,494 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 17:24:17,494 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-04 17:24:17,494 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 17:24:17,494 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:24:17,494 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-04 17:24:17,494 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-04 17:24:17,495 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:24:17,495 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-04 17:24:17,495 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 17:24:17,495 - INFO - [步骤 3/5] 等待 '南京市残疾人联合会' 的 Cookie 数据...
2025-08-04 17:24:17,495 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 17:24:18,496 - INFO - 检测到Cookie文件已生成。
2025-08-04 17:24:18,497 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 17:24:18,498 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 17:24:18,498 - INFO - [步骤 4/5] 停止 '南京市残疾人联合会' 的 Cookie 抓取器...
2025-08-04 17:24:18,498 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 17:24:18,499 - INFO - 正在停止Cookie抓取器 (PID: 26464)...
2025-08-04 17:24:18,501 - INFO - Cookie抓取器已成功终止。
2025-08-04 17:24:18,502 - INFO - 正在验证并清理代理设置...
2025-08-04 17:24:18,503 - INFO - === 开始重置网络状态 ===
2025-08-04 17:24:18,504 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 17:24:18,637 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 17:24:18,637 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 17:24:18,637 - INFO - 系统代理已成功关闭
2025-08-04 17:24:18,638 - INFO - ✅ 代理关闭操作
2025-08-04 17:24:18,638 - INFO - 🔗 正在验证网络连接...
2025-08-04 17:24:20,628 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:24:20,628 - INFO - ✅ 网络状态重置验证完成
2025-08-04 17:24:20,629 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 17:24:22,419 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 17:24:22,420 - INFO - ✅ 网络连接验证正常
2025-08-04 17:24:25,421 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 17:24:25,421 - INFO - ⏳ 等待Cookie在微信服务器端激活... (15秒)
2025-08-04 17:24:40,422 - INFO - [步骤 5/5] 开始爬取 '南京市残疾人联合会' 的文章...
2025-08-04 17:24:40,423 - INFO - 🔄 尝试第 1/3 次爬取...
2025-08-04 17:24:40,530 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
2025-08-04 17:24:50,531 - INFO - 🔄 尝试第 2/3 次爬取...
2025-08-04 17:24:50,694 - WARNING - ⚠️ Cookie验证失败，10秒后重试...
